import { Stage } from './store/types';

export const routes = {
  orders: {
    create: '/orders/create',
    edit: '/orders/edit',
    convert: '/orders/convert',
    connect: '/orders/connect',
  },
  lists: {
    purchaseIntentions: '/lists/purchase-intentions',
    openSales: '/lists/open-sales',
    preProductionOrders: '/lists/orders',
    inProductionOrders: '/lists/orders-in-production',
    inDistributionOrders: '/lists/orders-in-distribution',
    atDealershipsOrders: '/lists/orders-at-dealerships',
    totalLossOrders: '/lists/orders-in-total-loss',
    miniPurchaseIntentions: '/lists/mini-purchase-intentions',
    miniEditableOrders: '/lists/mini-editable-orders',
    miniPreProductionOrders: '/lists/mini-pre-production-orders',
    miniInProductionOrders: '/lists/mini-in-production-orders',
    miniInDistributionOrders: '/lists/mini-in-distribution-orders',
    miniAtDealerOrders: '/lists/mini-at-dealer-orders',
  },
  monitoring: '/monitoring',
  navigationSystem: '/navigation-system',
  testing: '/testing',
  test: '/test',
  audit: '/audit',
  external_urls: {
    quotaDashboardRelativePaddockPath: `quota-dashboard`,
    quotaDashboardFullPath: (stage: Stage) =>
      `https://quota-dashboard.cora${stage !== 'prod' ? '-' + stage : ''}.dpp.porsche.com`,
    doriRelativePaddockPath: 'dori',
    doriFullPath: (stage: Stage) => `https://dori${stage !== 'prod' ? '-' + stage : ''}.dpp.porsche.com`,
    doriOrderDetailsRelativePath: (ncoId: string) => `/?newCarOrderId=${ncoId}`,
    paddockFullPath: (stage: Stage) => `https://paddock${stage !== 'prod' ? '-' + stage : ''}.dpp.porsche.com`,
  },
} as const;
