import {
  initDataSourceForIntTest,
  prepareDynamodb,
  cleanupDynamodb,
  buildLambdaArn,
  createStandartOrgRelPattern,
  createSqsEvent,
  invokeGenericLambda,
  pollNotifications,
  createInboundEvent,
} from '../../../../utils/integration-test-helpers';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { DataSource, Repository } from 'typeorm';
import {
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { CoraNCOBaseApiRequest } from '../../../../../lib/types/new-car-order-types';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import { ncoApiToDbObj } from '../../../../utils/utils-typeorm';
import { CoraMdDealer, CoraMdImporter } from '../../../../../lib/types/masterdata-types';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import {
  DefaultEventHandlerResult,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  SpecialStatusCode,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';

const lambdaArn = buildLambdaArn(`event-handler-${OneVmsEventHandlerKey.CANCEL_NCO}`);
const orgRelTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.tableName);
const mdDlrTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_DEALER_TABLE_NAME}`;
const mdImpTableName = `${Constants.MASTER_DATA_APP_NAME}-dev-${Constants.DYNAMODB_BOSS_IMPORTER_TABLE_NAME}`;
const subTxTableName = Constants.buildResourceName('dev', Constants.DYNAMODB_SUBTRANSACTION_TABLE_PARAMS.tableName);

const { ids, ots, scs, orgRels } = createStandartOrgRelPattern('cancel-nco');

const orgRelPks = orgRels.map((org) => ({
  pk_ppn_id: org.pk_ppn_id,
  parent_ppn_id: org.parent_ppn_id,
}));

const mdDealer: CoraMdDealer = ids.dealer01.md_org;
const mdImporter: CoraMdImporter = ids.importer.md_org;

//auth context apps test data
const appsWithVisibilityImp = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [
    {
      role: 'integration_test_role',
      modelTypeVisibility: 'IMP',
    },
  ],
};

const appsWithNoRole = {
  [Constants.APPLICATION_NAME_TO_AUTHORIZE]: [],
};

//existing nco test objects
const oldCarOrderObject: NewCarOrderModel = {
  pk_new_car_order_id: 'ITCancel01',
  dealer_number: mdDealer.sk_dealer_number,
  importer_code: mdImporter.code,
  importer_number: mdDealer.pk_importer_number,
  model_type: 'MT0001',
  model_year: '2030',
  order_type: ots.otNoCustomerRel.pk_order_type,
  quota_month: '2023-11',
  requested_dealer_delivery_date: '2024-01-01',
  shipping_code: scs.scBlacklistAllow.pk_shipping_code,
  receiving_port_code: 'ItCancelNcoPc1',
  created_by: 'IntegrationTester',
  modified_by: 'IntegrationTester',
  cnr: 'C00',
  order_status_onevms_code: 'IntegrationTestOrderStatus',
  order_status_onevms_error_code: 'IntegrationTestErrorStatus',
  order_invoice_onevms_code: 'IntegrationTestInvoiceStatus',
  order_status_onevms_timestamp_last_change: '2024-12-06T10:21:02.876Z',
  changed_by_system: OneVmsSourceSystemKey.CORA,
  configuration: {
    created_by: 'IntegrationTester',
    modified_by: 'IntegrationTester',
    ordered_options: [
      {
        created_by: 'IntegrationTester',
        modified_by: 'IntegrationTester',
        option_id: 'OPTION1',
        option_type: 'OPTIONTYPE1',
        referenced_package: 'RP1',
        referenced_package_type: 'RPTYPE1',
        referenced_package_sort_order: 1,
        package_content_sort_order: 1,
        option_subtype: 'OST1',
        option_subtype_value: 'OST_VALUE1',
        content: [
          {
            created_by: 'IntegrationTester',
            modified_by: 'IntegrationTester',
            option_id: 'CONTENT1',
            option_type: 'CONTENTTYPE1',
            referenced_package: 'RP1',
            referenced_package_type: 'RPTYPE1',
            referenced_package_sort_order: 1,
            package_content_sort_order: 1,
            option_subtype: 'OST1',
            option_subtype_value: 'OST_VALUE1',
          },
        ],
      },
    ],
    technical_options: null,
  },
  configuration_expire: { dummyProp: 'iAmDummy' },
};

//default cancel request obj
const defaultCancelNcoReqObject = { cancellation_reason: Constants.CANCELLATION_REASONS[0].id };
//default cancel sqs event
const defaultCancelNcoDataSqsEvent = {
  event_type: OneVmsEventKey.CANCEL,
  user_auth_context: {
    organizationId: ids.allOrgsOrgId,
    kasApplications: appsWithVisibilityImp,
    username: 'IntegrationTester',
    firstName: 'IntegrationTester',
    lastName: 'IntegrationTester',
    porschePartnerNo: 'IntegrationTester',
    displayName: 'IntegrationTester',
  },
  nco_id: oldCarOrderObject.pk_new_car_order_id,
  modified_at: oldCarOrderObject.modified_at!,
  payload: defaultCancelNcoReqObject,
  source_system: OneVmsSourceSystemKey.CORA_USER,
};

let dataSource: DataSource;
let ncoRepo: Repository<NewCarOrderModel>;
let outboundEventMapping: OutboundProcessMappingModel | null;

function deepcopyNcoEntity(oldNco: NewCarOrderModel): NewCarOrderModel {
  return ncoApiToDbObj(oldNco as unknown as CoraNCOBaseApiRequest, 'IntegrationTester', oldNco);
}

describe('Cancel NCO Event Handler Integration Test', () => {
  beforeAll(async () => {
    dataSource = await initDataSourceForIntTest([
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
      OutboundProcessMappingModel,
    ]);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);

    //save nco test objects
    const oldCarOrderObjectRes = await ncoRepo.save(deepcopyNcoEntity(oldCarOrderObject));

    //update modified_at fields because valid values are needed in request body
    oldCarOrderObject.modified_at = oldCarOrderObjectRes.modified_at;
    defaultCancelNcoDataSqsEvent.modified_at = oldCarOrderObjectRes.modified_at!;

    //save outbound status mapping
    outboundEventMapping = await dataSource.getRepository(OutboundProcessMappingModel).findOneBy({
      source_event_handler: OneVmsEventHandlerKey.CANCEL_NCO,
      event_result: DefaultEventHandlerResult.SUCCESS,
    });

    await prepareDynamodb([
      { tableName: orgRelTableName, objs: orgRels },
      {
        tableName: mdDlrTableName,
        objs: [ids.dealer01.md_org],
      },
      { tableName: mdImpTableName, objs: [ids.importer.md_org] },
    ]);
  }, 120000);

  afterAll(async () => {
    //delete nco test objects
    await ncoRepo.delete({
      pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id,
    });

    //delete audit trails
    await dataSource.getRepository(NewCarOrderAuditTrailModel).delete({
      pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id,
    });

    await dataSource.destroy();

    await cleanupDynamodb([
      {
        tableName: orgRelTableName,
        pks: orgRelPks,
      },
      {
        tableName: mdDlrTableName,
        pks: [
          {
            pk_importer_number: mdDealer.pk_importer_number,
            sk_dealer_number: mdDealer.sk_dealer_number,
          },
        ],
      },
      {
        tableName: mdImpTableName,
        pks: [{ pk_importer_number: mdImporter.pk_importer_number }],
      },
    ]);
  }, 120000);

  it('Should NOT cancel nco and return fail if input is missing required props', async () => {
    const eventPayload = createInboundEvent(defaultCancelNcoDataSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, payload: undefined }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);

    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('SQS record is not valid');

    const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
    expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
    expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  });

  it('Should NOT cancel nco and return fail if auth context is faulty', async () => {
    const eventPayload = createInboundEvent(defaultCancelNcoDataSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        user_auth_context: {
          ...eventPayload.user_auth_context,
          kasApplications: appsWithNoRole,
        },
      },
    ]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to get the visibility level');

    const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
    expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
    expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  });

  it('Should NOT cancel nco and return fail if nco cannot be found', async () => {
    const eventPayload = createInboundEvent(defaultCancelNcoDataSqsEvent);
    const event = createSqsEvent([
      {
        ...eventPayload,
        nco_id: 'NOT_VALID_NCOID',
      },
    ]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Failed to find order with id');

    const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
    expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
    expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  });

  it('Should NOT cancel nco and return fail if modified_at does not match', async () => {
    const eventPayload = createInboundEvent(defaultCancelNcoDataSqsEvent);
    const event = createSqsEvent([{ ...eventPayload, modified_at: new Date().toISOString() }]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const subTransactions = await pollNotifications(subTxTableName, eventPayload.transaction_id);
    expect(subTransactions.length).toBeGreaterThan(0);
    expect(subTransactions[0].status).toEqual(NotificationStatus.EVENT_HANDLER_NIO);
    expect(subTransactions[0].details).toContain('Nco was changed by someone else');

    const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_status_onevms_code).toEqual(oldCarOrderObject.order_status_onevms_code);
    expect(ncoDb?.order_status_onevms_error_code).toEqual(oldCarOrderObject.order_status_onevms_error_code);
    expect(ncoDb?.order_invoice_onevms_code).toEqual(oldCarOrderObject.order_invoice_onevms_code);
  });

  it('Should cancel nco and return no fails ', async () => {
    const eventPayload = createInboundEvent(defaultCancelNcoDataSqsEvent);
    const event = createSqsEvent([eventPayload]);
    const res = await invokeGenericLambda<SQSBatchResponseWithError>(lambdaArn, event);
    expect(res.batchItemFailures).toBeDefined();
    expect(res.batchItemFailures.length).toBe(0);
    const ncoDb = await ncoRepo.findOneBy({ pk_new_car_order_id: oldCarOrderObject.pk_new_car_order_id });
    expect(ncoDb).not.toBeNull();
    expect(ncoDb?.order_status_onevms_code).toEqual(outboundEventMapping!.order_status_code);
    expect(ncoDb?.order_status_onevms_error_code).toEqual(SpecialStatusCode.NONE);
    expect(ncoDb?.order_invoice_onevms_code).toEqual(SpecialStatusCode.NONE);
    expect(ncoDb?.cancellation_reason).toEqual(defaultCancelNcoDataSqsEvent.payload.cancellation_reason);
  });
});
