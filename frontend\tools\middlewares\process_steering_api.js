const { v4: uuidv4 } = require('uuid');

// Middleware to handle various actions for the new car order process
// List of actions with their respective checks
const ncoEndpoint = '/new-car-order';
const actions = [
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}/cancel`),
    event_type: 'cancel',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.endsWith('/convert'),
    event_type: 'convert-pi',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.endsWith('/connect'),
    event_type: 'connect-pi',
  },
  {
    check: (req) => req.method === 'POST' && !req.url.includes('copy') && !req.url.endsWith('get'),
    event_type: 'create',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}`),
    event_type: 'update',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}/move-to-dealer-inventory`),
    event_type: 'move-to-dealer-inventory',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}/remove-from-dealer-inventory`),
    event_type: 'remove-from-dealer-inventory',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}/deallocate`),
    event_type: 'deallocate',
  },
  {
    check: (req) => req.method === 'PATCH' && req.url.startsWith(`${ncoEndpoint}/update-core-data`),
    event_type: 'update-core-data',
  },
  // Please add more actions as needed
];

module.exports = function (req, res, next) {
  // Check if the request matches any of the defined actions
  const matchedAction = actions.find((action) => action.check(req));
  if (!matchedAction) {
    return next();
  }

  if (matchedAction.event_type === 'create') {
    // For create action, we generate a new car order ID
    req.body.pk_new_car_order_id = 'GE' + Math.random().toString(36).slice(2, 7);
  }
  const randomSuccess = () => Math.random() < 0.8; // 80% chance of success, because I don't like odd numbers
  const isSuccess = randomSuccess();

  if (!isSuccess) {
    return res.status(500).json({
      message: 'Internal server error. Seems like you are not having a lucky day :(',
    });
  }

  return res.status(200).json({
    message: 'Action was a total success',
    data: {
      transaction_id: uuidv4(),
      action_at: new Date().toISOString(),
      event_type: matchedAction.event_type,
    },
  });
};
