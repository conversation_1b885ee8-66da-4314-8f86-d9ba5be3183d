import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getAuthContext, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { QueryCommand } from '@aws-sdk/lib-dynamodb';
import { CoraQuota } from '../../../../lib/types/quota-api-types';
import { extractDlrsAndImpsFromOrgs, getAllAuthorizedOrgs } from '../../../utils/validation-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { secretCache } from '../../../utils/secret-cache';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { getModelTypeVisibility } from '../../../utils/utils-typeorm';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const quotaTableName = getEnvVarWithAssert('QUOTA_TABLE_NAME');
const orgRelsTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const fetchQuotaFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const importerNumber = event.queryStringParameters ? event.queryStringParameters['importer_number'] : undefined;
  const dealerNumber = event.queryStringParameters ? event.queryStringParameters['dealer_number'] : undefined;
  const modelType = event.queryStringParameters ? event.queryStringParameters['model_type'] : undefined;
  const modelYear = event.queryStringParameters ? event.queryStringParameters['model_year'] : undefined;

  if (!importerNumber || !dealerNumber || !modelType || !modelYear) {
    logger.log(
      LogLevel.WARN,
      'importerNumber, dealerNumber, modelType or modelYear query param is required but missing',
      { data: sanitizeApiGwEvent({ event }, logger) },
    );
    return sendFail(
      {
        message: 'importerNumber, dealerNumber, modelType or modelYear query param is required but missing',
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  //look at the first entry for the cora application and take the visibility from there
  const userAttributes = getAuthContext({ event: event }, logger);
  const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;

  //get the org id of the user from auth context
  const ppnId = userAttributes?.organizationId;

  if (!ppnId || !visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId or visibility level', {
      data: sanitizeApiGwEvent({ event: event }, logger),
    });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  try {
    const authorizedOrgs = await getAllAuthorizedOrgs(
      {
        dynamoDb: dynamoDb,
        orgRelsTableName: orgRelsTableName,
        ppnId: ppnId,
      },
      logger,
    );
    const { dlrs } = extractDlrsAndImpsFromOrgs({ orgs: authorizedOrgs }, logger);
    logger.log(LogLevel.INFO, `Collected ${dlrs.length} allowed Dealers`);

    if (!dlrs.find((dlr) => dlr.dealer_number === dealerNumber && dlr.importer_number === importerNumber)) {
      logger.log(LogLevel.WARN, 'User is not allowed on provided dealer or importer', {
        data: sanitizeApiGwEvent({ event: event }, logger),
      });
      return sendFail({ message: 'Not allowed', status: 403, reqHeaders: event.headers }, logger);
    }

    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [ModelTypeVisibilityModel]);

    const modelTypevisibilities = await getModelTypeVisibility(
      {
        mtvRespoitory: dataSource.getRepository(ModelTypeVisibilityModel),
        importer_number: importerNumber,
        model_type: modelType,
        model_year: modelYear,
        visibilityLevel: visibilityLevel,
      },
      logger,
    );

    if (modelTypevisibilities.length == 0) {
      logger.log(LogLevel.WARN, 'User is not allowed on provided model type data', {
        data: sanitizeApiGwEvent({ event: event }, logger),
      });
      return sendFail({ message: 'Not allowed', status: 403, reqHeaders: event.headers }, logger);
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Failed to load allowed model types', { data: error });
    return sendFail({ message: 'Failed to load allowed model types', status: 500, reqHeaders: event.headers }, logger);
  }

  const partitionKey = `QA-${importerNumber}-${dealerNumber}-${modelType}-${modelYear}`;
  //get all Quotas for the specified key
  const cmd = new QueryCommand({
    TableName: quotaTableName,
    KeyConditionExpression: `quota_id_without_month = :value`,
    ExpressionAttributeValues: { ':value': partitionKey },
  });

  let quotaResponse: CoraQuota[] | undefined;
  try {
    const dynamoResult = await dynamoDb.send(cmd);
    quotaResponse = dynamoResult.Items as CoraQuota[] | undefined;
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error loading quotas', { data: error });
    return sendFail({ message: 'Error loading quotas', status: 500, reqHeaders: event.headers }, logger);
  }

  // Add some mock quotas to the result
  if (process.env.MOCK_QUOTA_RESPONSE === 'true') {
    const mockQuotas: CoraQuota[] = [];
    const now = new Date();
    for (let i = 0; i < 3; i++) {
      const qm = new Date(now.setMonth(now.getMonth() + i)).toISOString().slice(0, 7);
      mockQuotas.push({
        quota_id_without_month: `QA-${importerNumber}-${dealerNumber}-${modelType}-${modelYear}`,
        quota_month: qm,
        consumed_new_car_order_ids: [],
        created_at: '',
        created_by: '',
        dealer_number: parseInt(dealerNumber),
        importer_number: parseInt(importerNumber),
        last_modified_at: '',
        last_modified_by: '',
        model_type: modelType,
        model_year: Number(modelYear),
        quota_consumed: 0,
        quota_count: 10,
        quota_id: `QA-${importerNumber}-${dealerNumber}-${modelType}-${modelYear}-${qm}`,
        quota_open: 10,
      });
    }
    quotaResponse = quotaResponse ?? [];
    logger.log(LogLevel.WARN, 'Adding Mockquotas', { data: mockQuotas });
    quotaResponse = quotaResponse.concat(mockQuotas);
  }

  if (quotaResponse) {
    return sendSuccess({ body: quotaResponse, reqHeaders: event.headers }, logger);
  } else {
    return sendFail({ message: 'Error loading quotas', status: 404, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-quotas', LogLevel.TRACE)(event, context, fetchQuotaFunc);
