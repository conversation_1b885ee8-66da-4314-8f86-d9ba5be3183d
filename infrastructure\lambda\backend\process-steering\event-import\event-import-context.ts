import { <PERSON>fkaAdapter } from '../../../utils/kafka';
import { secretCache } from '../../../utils/secret-cache';
import { getEnvVarWithAssert, pushNotificationsToKafka } from '../../../utils/utils';
import { Kas<PERSON><PERSON><PERSON><PERSON>Logger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  InboundEventDispatcherEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
} from '../../../../lib/types/process-steering-types';
import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { DataSource } from 'typeorm';
import { Class } from 'type-fest';
import { createTypeORMDataSource } from '../../../config/typeorm-config';

export class EventImportContext {
  public static readonly auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');
  public static readonly kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
  public static readonly KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
  public static readonly KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
  public static readonly stage = getEnvVarWithAssert('STAGE');

  public static eventHandlerKey: OneVmsEventKey;
  public static logger: KasLambdaLogger;
  public static kafkaAdapter: KafkaAdapter;
  public static sqsClient: SQSClient;

  public static dbModelEntities: Class[];
  private static dataSource: DataSource | undefined;

  private constructor() {}

  public static init(eventHandlerKey: OneVmsEventKey, dbModelEntities: Class[]): void {
    this.logger = new KasLambdaLogger(eventHandlerKey + '-import-event', LogLevel.TRACE);
    this.eventHandlerKey = eventHandlerKey;
    secretCache.initCache(this.auroraSecretArn, this.kafkaSecretArn);
    this.kafkaAdapter = new KafkaAdapter({
      kafka_brokers: this.KAFKA_BROKERS,
      kafka_secret_arn: this.kafkaSecretArn,
      logger: this.logger,
    });
    this.sqsClient = new SQSClient({ tls: true });
    this.dbModelEntities = [...dbModelEntities];
  }

  public static async getDataSource(): Promise<DataSource> {
    if (!this.dataSource?.isInitialized) {
      this.dataSource = await createTypeORMDataSource(
        this.logger,
        this.auroraSecretArn,
        this.stage,
        this.dbModelEntities,
      );
    }
    return this.dataSource;
  }

  public static async reconnectDataSource(): Promise<DataSource> {
    if (this.dataSource?.isInitialized) {
      await this.dataSource.destroy();
    }
    return this.getDataSource();
  }

  public static async sendToDispatcherSqs(event: InboundEventDispatcherEvent): Promise<void> {
    const dispatcherQueueUrl = getEnvVarWithAssert('DISPATCHER_QUEUE_URL');
    const res = await this.sqsClient.send(
      new SendMessageCommand({
        QueueUrl: dispatcherQueueUrl,
        MessageBody: JSON.stringify(event),
      }),
    );

    if (!res.MessageId) {
      const errMessage = 'Failed to send event to SQS';
      this.logger.log(LogLevel.ERROR, errMessage, { data: res });
      throw new Error(`${errMessage}. Missing MessageId in SQS response`);
    }

    this.logger.log(
      LogLevel.INFO,
      `Successfully sent api event to dispatcher queue. Creating and publishing notification now`,
    );
    const notificationEvent: NotificationKafkaEvent = {
      transaction_id: event.transaction_id,
      sub_transaction_id: event.ncos_info![0].sub_transaction_id,
      event_type: event.event_type,
      action_at: event.action_at,
      source_system: event.source_system,
      status: NotificationStatus.ACCEPTED,
      action_by: event.source_system,
    };
    await this.publishKafkaNotifications([notificationEvent]);
  }

  public static async publishKafkaNotifications(events: NotificationKafkaEvent[]): Promise<void> {
    await pushNotificationsToKafka(events, this.KAFKA_TOPIC_NOTIFICATION, this.kafkaAdapter, this.logger);
  }
}
