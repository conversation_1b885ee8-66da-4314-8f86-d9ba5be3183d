const fs = require('fs');
const path = require('path');

const prefix = '/purchase-intention';
module.exports = function (req, res, next) {
  if (req.method === 'POST' && req.url.startsWith(prefix) && !!req.body) {
    console.log('🔥 Purchase Intention Middleware HIT');
    console.log('📦 Payload:', JSON.stringify(req.body, null, 2));

    fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
      if (err) {
        console.error('Failed to read db.json:', err);
        return res.status(500).send({ message: 'Internal Server Error' });
      }

      try {
        const json = JSON.parse(data);
        let pis = json['purchase-intention'] || [];

        const filters = req.body.filterModel || {};
        const sortModel = req.body.sortModel || [];
        const startRow = req.body.startRow ?? 0;
        const endRow = req.body.endRow ?? 100;

        // Filtrare după filterModel
        for (const [field, filter] of Object.entries(filters)) {
          if (filter.filterType === 'set' && Array.isArray(filter.values)) {
            pis = pis.filter((item) => filter.values.includes(item[field]));
          } else if (filter.filter) {
            // Filter text: verifică dacă valoarea conține textul din filter.filter (case insensitive)
            const filterText = filter.filter.toLowerCase();
            pis = pis.filter((item) => (item[field] ?? '').toString().toLowerCase().includes(filterText));
          }
        }

        // Sortare după sortModel (doar primul criteriu pentru simplitate)
        if (sortModel.length > 0) {
          const { colId, sort } = sortModel[0];
          pis.sort((a, b) => {
            const valA = a[colId];
            const valB = b[colId];
            if (valA == null) return 1;
            if (valB == null) return -1;
            if (valA === valB) return 0;
            if (sort === 'asc') {
              return valA < valB ? -1 : 1;
            } else {
              return valA > valB ? -1 : 1;
            }
          });
        }

        // Paginare
        const pagedData = pis.slice(startRow, endRow);

        if (pagedData.length > 0) {
          return res.status(200).send({
            data: pagedData,
            rowCount: pis.length,
          });
        } else {
          return res.status(404).send({ message: 'No matching records found' });
        }
      } catch (e) {
        console.error('JSON parse or processing error:', e);
        return res.status(500).send({ message: 'Internal Server Error' });
      }
    });
  } else {
    next();
  }
};
