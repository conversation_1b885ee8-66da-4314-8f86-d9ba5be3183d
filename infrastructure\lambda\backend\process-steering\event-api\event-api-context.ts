import { SendMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { KasAuthEndpointResponse } from '@kas-resources/constructs-rbam/src/lib/lambda/utils/types';
import { Kas<PERSON>ambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { APIGatewayProxyEvent, APIGatewayProxyEventHeaders, APIGatewayProxyResult } from 'aws-lambda';
import { v4 as uuidv4 } from 'uuid';
import {
  CorePayload,
  InboundApiEventResponse,
  InboundEventDispatcherEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../../lib/types/process-steering-types';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { getAuthContext, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { ApiHandlerError } from '../../../utils/errors';
import { KafkaAdapter } from '../../../utils/kafka';
import { secretCache } from '../../../utils/secret-cache';
import { getEnvVarWithAssert, pushNotificationsToKafka } from '../../../utils/utils';

export class EventApiContext {
  public static readonly applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
  public static readonly kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
  public static readonly KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
  public static readonly KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];
  public static readonly stage = getEnvVarWithAssert('STAGE');

  public static eventHandlerKey: OneVmsEventKey;
  public static logger: KasLambdaLogger;
  public static kafkaAdapter: KafkaAdapter;
  public static sqsClient: SQSClient;

  // No instances (Max 1 per Lambda, so everything here is static)
  private constructor() {}

  public static init(eventHandlerKey: OneVmsEventKey): void {
    this.logger = new KasLambdaLogger(eventHandlerKey + '-api-event', LogLevel.TRACE);
    this.eventHandlerKey = eventHandlerKey;
    secretCache.initCache(this.kafkaSecretArn);
    this.kafkaAdapter = new KafkaAdapter({
      kafka_brokers: this.KAFKA_BROKERS,
      kafka_secret_arn: this.kafkaSecretArn,
      logger: this.logger,
    });
    this.sqsClient = new SQSClient({ tls: true });
  }

  /**
   * Merges the validated endpoint-specific payload with common fields that every event needs.
   * This function also generates a unique transaction ID and a timestamp.
   *
   * @param validatedPayload The payload processed and validated by the endpoint-specific code.
   * @param userAttributes The authenticated user information extracted from the API event.
   * @returns The dispatcher
   * event payload with common fields injected.
   */
  public static buildDispatcherEvent(
    validatedPayload: CorePayload,
    userAttributes: KasAuthEndpointResponse,
  ): InboundEventDispatcherEvent {
    const transactionId = uuidv4();
    return {
      ...validatedPayload,
      sub_transaction_id: validatedPayload.sub_transaction_id,
      transaction_id: transactionId,
      action_at: new Date().toISOString(),
      user_auth_context: userAttributes,
      source_system: OneVmsSourceSystemKey.CORA_USER,
    };
  }

  /**
   * Sends the constructed event payload to the dispatcher SQS queue.
   * This helper creates a new SQS client, builds the SQS message, and sends it.
   *
   * @param eventPayload The final event payload that needs to be sent.
   * @returns A Promise that resolves if the send operation is successful.
   * @throws An error if the SQS operation does not return a MessageId.
   */
  public static async sendEventToDispatcherSqs(eventPayload: InboundEventDispatcherEvent): Promise<void> {
    let errMessage: string;
    const dispatcherQueueUrl = getEnvVarWithAssert('DISPATCHER_QUEUE_URL');
    const params = {
      QueueUrl: dispatcherQueueUrl,
      MessageBody: JSON.stringify(eventPayload),
    };

    const res = await this.sqsClient.send(new SendMessageCommand(params));
    if (!res.MessageId) {
      errMessage = 'Failed to send event to SQS';
      this.logger.log(LogLevel.ERROR, errMessage, { data: res });
      throw new Error(`${errMessage}. Missing MessageId in SQS response`);
    }
    this.logger.log(LogLevel.INFO, `Successfully sent api event to dispatcher queue`);
  }

  /**
   * Sends Kafka notifications based on a list of nco information objects.
   * This helper builds notification events from the given data, then publishes them using the Kafka adapter.
   *
   * @param dispatchedEvent The event which was successfully send to the dispatcher queue.
   * @returns A Promise that resolves when the notifications are successfully published.
   */
  public static async enrichAndPublishNotifications(
    dispatchedEvent: InboundEventDispatcherEvent,
  ): Promise<NotificationKafkaEvent[]> {
    let notificationEvents: NotificationKafkaEvent[] = [];
    if (dispatchedEvent.ncos_info) {
      notificationEvents = dispatchedEvent.ncos_info.map((nco) => ({
        transaction_id: dispatchedEvent.transaction_id,
        event_type: dispatchedEvent.event_type,
        source_system: dispatchedEvent.source_system,
        sub_transaction_id: nco.sub_transaction_id,
        action_by: dispatchedEvent.user_auth_context?.username ?? 'unknown',
        action_at: dispatchedEvent.action_at,
        nco_id: nco.pk_new_car_order_id,
        status: NotificationStatus.ACCEPTED,
        transaction_obj_amount: dispatchedEvent.ncos_info!.length,
      }));
    } else {
      notificationEvents = [
        {
          transaction_id: dispatchedEvent.transaction_id,
          event_type: dispatchedEvent.event_type,
          source_system: dispatchedEvent.source_system,
          sub_transaction_id: dispatchedEvent.sub_transaction_id ?? uuidv4(),
          action_by: dispatchedEvent.user_auth_context?.username ?? 'unknown',
          action_at: dispatchedEvent.action_at,
          status: NotificationStatus.ACCEPTED,
          transaction_obj_amount: 1,
        },
      ];
    }

    await pushNotificationsToKafka(notificationEvents, this.KAFKA_TOPIC_NOTIFICATION, this.kafkaAdapter, this.logger);
    this.logger.log(LogLevel.INFO, `Sent ${notificationEvents.length} Kafka notifications`);
    return notificationEvents;
  }

  public static async pushNotificationsToKafka(events: NotificationKafkaEvent[]): Promise<void> {
    return pushNotificationsToKafka(events, this.KAFKA_TOPIC_NOTIFICATION, this.kafkaAdapter, this.logger);
  }

  public static async handleDispatcherEvent(
    dispatcherEvent: InboundEventDispatcherEvent,
    incomingEventHeaders: APIGatewayProxyEventHeaders,
  ): Promise<APIGatewayProxyResult> {
    //send dispatcher sqs event and single kafka accepted events for every nco
    try {
      await this.sendEventToDispatcherSqs(dispatcherEvent);
    } catch (e: unknown) {
      this.logger.log(LogLevel.ERROR, (e as Error).message, { data: e });
      return sendFail({ message: (e as Error).message, status: 500, reqHeaders: incomingEventHeaders }, this.logger);
    }

    //send kafka notification events
    try {
      const notificationEvents = await this.enrichAndPublishNotifications(dispatcherEvent);
      this.logger.log(LogLevel.INFO, `Successfully sent ${notificationEvents.length} kafka event notifications`);
    } catch (e) {
      this.logger.log(LogLevel.ERROR, `Error while pushing api event into sqs`, { data: e });
      //do not return fail as the event was pushed into sqs successfully?
    }

    //return success response
    const res_body: InboundApiEventResponse = {
      message: `Successfully sent api event to dispatcher, wait for result in notification center`,
      data: {
        transaction_id: dispatcherEvent.transaction_id,
        action_at: dispatcherEvent.action_at,
        event_type: dispatcherEvent.event_type,
      },
    };
    return sendSuccess({ body: res_body, reqHeaders: incomingEventHeaders }, this.logger);
  }
  public static commonEventInputValidation<T>(
    event: APIGatewayProxyEvent,
    objectValidator: ObjectValidator<T>,
  ): { body_validated: T; userAttributes: KasAuthEndpointResponse } {
    const userAttributes = getAuthContext({ event }, this.logger);
    if (!userAttributes) {
      this.logger.log(LogLevel.WARN, 'Missing auth context');
      throw new ApiHandlerError({ message: 'Missing auth context', statusCode: 401 });
    }

    // Parse and validate request body
    const body = JSON.parse(event.body ?? '{}') as unknown;
    const [body_validated, validation_errors] = objectValidator.validate(body);
    if (body_validated === null) {
      this.logger.log(LogLevel.WARN, 'AJV validation failed', { data: validation_errors });
      throw new ApiHandlerError({
        message: 'AJV validation failed with: ' + JSON.stringify(validation_errors),
        statusCode: 400,
      });
    }
    return { body_validated, userAttributes };
  }
}
