import { getEnvVarWithAssert } from '../../../utils/utils';
import { KafkaAdapter } from '../../../utils/kafka';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { NewCarOrderAuditTrailModel } from '../../../../lib/entities/new-car-order-audit-trail-model';
import {
  ExportResult,
  KafkaObj,
  KafkaObjsWrapper,
  KafkaObjTyp,
  NcoExportActionType,
  NewCarOrderKafkaObject,
  P06_EXPORT_ACTION_TYPES,
} from '../../export-nco/types';
import { flatToKafkaExportNewCarOrderObj } from './utils';
import { NotificationKafkaEvent, OneVmsSourceSystemKey } from '../../../../lib/types/process-steering-types';
import { removeLocalOptionsFromNco } from '../../../utils/utils-typeorm';

const KAFKA_TOPIC_P06 = getEnvVarWithAssert('KAFKA_TOPIC_P06');

export async function processP06Export(
  notification: NotificationKafkaEvent,
  auditTrails: NewCarOrderAuditTrailModel[],
  kafkaAdapter: KafkaAdapter,
  logger: KasLambdaLogger,
): Promise<ExportResult> {
  const ncoId = notification.nco_id!;
  const correlationId = notification.transaction_id;
  const defaultResult: ExportResult = {
    exportedIds: [],
    failed: false,
  };

  try {
    if (auditTrails.length === 0) {
      logger.log(LogLevel.INFO, `No unexported audit trails found for object_id=${ncoId}, skipping export.`);
      return defaultResult;
    }

    const kafkaExportObjs: KafkaObj<NewCarOrderKafkaObject>[] = [];

    for (const auditTrail of auditTrails) {
      logger.setObjectId(auditTrail.pk_new_car_order_id);

      if (!auditTrail.new_nco && !auditTrail.old_nco) {
        logger.log(LogLevel.ERROR, 'AuditTrail without new/old object, skipping.', { data: auditTrail });
        continue;
      }

      if (auditTrail.new_nco?.changed_by_system === OneVmsSourceSystemKey.P06) {
        logger.log(LogLevel.INFO, 'Skipping p06 export (changed_by_system === P06)', { data: auditTrail });
        continue;
      }

      //remove local options for p06 topic and convert to export object
      const exportObj: KafkaObj<NewCarOrderKafkaObject> = {
        id: auditTrail.pk_new_car_order_id,
        kafkaActionTyp: auditTrail.action_type,
        correlationid: auditTrail.action_correlation_id,
        importer: auditTrail.new_nco?.importer_number ?? auditTrail.old_nco?.importer_number,
        obj: flatToKafkaExportNewCarOrderObj(removeLocalOptionsFromNco(auditTrail.new_nco ?? auditTrail.old_nco!)),
      };

      kafkaExportObjs.push(JSON.parse(JSON.stringify(exportObj)) as KafkaObj<NewCarOrderKafkaObject>); // deep copy for mutation safety
    }

    // 3. Filter P06-specific rules
    const filteredExportObjs = kafkaExportObjs.filter((obj) => P06_EXPORT_ACTION_TYPES.includes(obj.kafkaActionTyp!));

    if (filteredExportObjs.length === 0) {
      logger.log(LogLevel.INFO, `No valid P06 exports left after filtering.`);
      return { exportedIds: [], failed: false };
    }

    // 4. Prepare payload for Kafka
    const kafkaObjsWrapper: KafkaObjsWrapper<NewCarOrderKafkaObject> = {
      kafkaObjTyp: KafkaObjTyp.NCO,
      kafkaObjs: filteredExportObjs.map((exportObj) => {
        const obj = exportObj.obj;

        obj.order_info.base_info.last_modified_by =
          obj.order_info.base_info.last_modified_by ?? obj.order_info.base_info.created_by;

        obj.appointment_date_info.production_logistic_dates.order_last_modification_timestamp =
          obj.appointment_date_info.production_logistic_dates.order_last_modification_timestamp ??
          obj.appointment_date_info.production_logistic_dates.order_creation_timestamp;

        return {
          ...exportObj,
          obj,
        };
      }),
      kafkaActionTyp: NcoExportActionType.UPDATE, //will be overwritten by individual item action type
    };

    // Push to P06 Kafka topic
    logger.log(LogLevel.INFO, `Exporting to P06 Kafka Topic`, {
      data: {
        topic: KAFKA_TOPIC_P06,
        payloadSize: kafkaObjsWrapper.kafkaObjs.length,
      },
    });

    await kafkaAdapter.pushObjsToTopic({
      topic: KAFKA_TOPIC_P06,
      kWrapper: kafkaObjsWrapper,
      correlationid: correlationId,
    });

    logger.log(LogLevel.INFO, `Successfully exported audit trails to P06 and marked them as exported`);
    return {
      exportedIds: [...new Set(filteredExportObjs.map((obj) => obj.id))],
      failed: false,
    };
  } catch (err) {
    logger.log(LogLevel.ERROR, 'P06 export failed', { data: err });
    return {
      exportedIds: [],
      failed: true,
      error: err instanceof Error ? err : new Error('Unknown P06 export error'),
    };
  }
}
