import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { KeyValueMtv, KafkaModeltypeVisibility } from '../../../../lib/types/model-type-types';
import { Kas<PERSON><PERSON>b<PERSON><PERSON>ogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { correlationHeader, getEnvVarWithAssert } from '../../../utils/utils';
import { secretCache } from '../../../utils/secret-cache';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { DataSource } from 'typeorm';
import { ObjectValidator } from '../../../../lib/utils/object-validation/object-validator';

const IMPORT_TOPIC = getEnvVarWithAssert('IMPORT_TOPIC');
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const logger = new KasLambdaLogger('boss-modeltype-visibility-sync', LogLevel.TRACE);
const objectValidator = new ObjectValidator<KafkaModeltypeVisibility>('KafkaModeltypeVisibility');
let dataSource: DataSource | undefined = undefined;

export const handler: Handler<MSKEvent, void> = async (event, context) => {
  logger.setRequestContext(context);
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  logger.log(LogLevel.DEBUG, `Got topic event, processing...`, {
    data: event.records,
    correlationId: context.awsRequestId,
  });

  const keyValueMtvs = Object.entries(event.records)
    .filter(([key]) => key.includes(IMPORT_TOPIC))
    .flatMap(([, value]) => value.map(parseRecord))
    .filter(Boolean) as KeyValueMtv[];

  logger.setCorrelationId(context.awsRequestId);
  logger.log(LogLevel.DEBUG, JSON.stringify(keyValueMtvs));

  await Promise.all([insertModelTypeVisibilities(keyValueMtvs)]);

  function parseRecord(record: MSKRecord): KeyValueMtv | null {
    try {
      const kafkaKey = Buffer.from(record.key, 'base64').toString('utf8');

      //record.value == undefined means mtv was deleted in BOSS
      if (!record.value) {
        return {
          key: kafkaKey,
          value: null,
        };
      }
      const kafkaModelBuff: Buffer = Buffer.from(record.value, 'base64');
      const kafkaModelObj = JSON.parse(kafkaModelBuff.toString('utf8')) as KafkaModeltypeVisibility | null;

      const [body_validated, validation_errors] = objectValidator.validate(kafkaModelObj);
      if (body_validated === null) {
        logger.log(LogLevel.WARN, 'Object validation failed, skipping object', {
          data: { error: validation_errors, object: kafkaModelObj },
        });
        return null;
      }

      // Check for ActionType deleted, set the value null
      let ce_type = undefined;
      for (const header of record.headers) {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        if (header.ce_type) {
          ce_type = Buffer.from(header.ce_type).toString();
        }
      }
      if (ce_type) {
        const action = ce_type.split('.').at(-1);
        if (action === 'deleted')
          return {
            key: kafkaKey,
            value: null,
          };
      }
      return {
        key: kafkaKey,
        value: kafkaModelObj,
      };
    } catch (e) {
      logger.log(LogLevel.ERROR, 'Could not parse model type visibility, terminating lambda.', {
        data: {
          value: record.value,
          error: e,
        },
        correlationId: correlationHeader(record),
      });
      throw new Error('Unable to parse record');
    }
  }

  async function insertModelTypeVisibilities(kvMtvs: KeyValueMtv[]): Promise<void> {
    if (kvMtvs.length === 0) return;

    // Track seen mtv keys to avoid duplicate entries.
    const seen = new Set<string>();
    const uniqueMtvs = kvMtvs.filter((keyValueMtv) => {
      // Check whether the mtv key has been seen
      if (!seen.has(keyValueMtv.key)) {
        seen.add(keyValueMtv.key); // Add the mtv key to the set.
        return true; // Keep the item.
      }
      return false; // Discard the item.
    });

    try {
      if (!dataSource) {
        dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [
          //stage hardcoded because w/e?? schema sync can not work with a reader secret
          ModelTypeVisibilityModel,
        ]);
      }

      const _add_mtvs = uniqueMtvs.filter((mtv) => mtv.value) as { value: KafkaModeltypeVisibility }[];
      const _delete_mtvs = uniqueMtvs.filter((mtv) => !mtv.value);

      const mtvsToWrite: ModelTypeVisibilityModel[] = _add_mtvs.map((mtv) => {
        return {
          cnr: mtv.value.cnr,
          role: mtv.value.role,
          importer_number: mtv.value.importer_number.toString(),
          model_type: mtv.value.model_type,
          my4: mtv.value.my4.toString(),
          modified_by: 'BOSS_SYNC',
          created_by: 'BOSS_SYNC',
          modified_at: mtv.value.modified_at,
          valid_from: mtv.value.valid_from,
        };
      });

      const mtvsToDelete: { importer_number: string; role: string; model_type: string; my4: string; cnr: string }[] =
        _delete_mtvs.map((mtv) => parseMtvKey(mtv.key));

      await dataSource.getRepository(ModelTypeVisibilityModel).save(mtvsToWrite);
      await dataSource.getRepository(ModelTypeVisibilityModel).remove(mtvsToDelete as ModelTypeVisibilityModel[]);
    } catch (error) {
      logger.log(LogLevel.ERROR, 'Error inserting records into DB', {
        data: { error: error },
      });
      throw error; // rethrowing the error after logging makes sure your lambda fails and logs error in CloudWatch
    }
  }
};

const parseMtvKey = (
  key: string,
): { importer_number: string; role: string; model_type: string; my4: string; cnr: string } => {
  const keyElements = key.split('_');
  if (keyElements.length != 5) {
    throw new Error('Unable to parse mtv key: ' + key);
  }

  return {
    importer_number: keyElements[0],
    role: keyElements[4],
    model_type: keyElements[2],
    my4: keyElements[3],
    cnr: keyElements[1],
  };
};
