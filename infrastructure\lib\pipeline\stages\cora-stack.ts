import { Stack, Tags } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { BackendStack } from '../../stacks/backend-stack';
import { FrontendStack } from '../../stacks/frontend-stack';
import { GlobalStack } from '../../stacks/global-stack';
import { BossSyncStack } from '../../stacks/boss-sync-stack';
import { QuotaSyncStack } from '../../stacks/quota-sync-stack';
import { ModelTypeTextsSyncStack } from '../../stacks/model-type-texts-sync-stack';
import { CoraStackProps, CoraStackPropsWithVpc } from '../../types_cdk/cdk-types';
import { PVMSOrderDataSyncStack } from '../../stacks/pvms-order-data-sync-stack';
import { AuroraDatabaseStack } from '../../stacks/aurora-database-stack';
import { AuditTrailStack } from '../../stacks/audit-trail-stack';
import { E2eTestStack } from '../../stacks/e2e-test-stack';
import { ProcessSteeringStack } from '../../stacks/process-steering-stack';
import { DisasterRecoveryStack } from '../../stacks/disaster-recovery-stack';
import { CSSOrderDataSyncStack } from '../../stacks/css-order-data-sync-stack';
import { P06OrderDataSyncStack } from '../../stacks/p06-order-data-sync-stack';

export class CoraStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackProps) {
    super(scope, id, props);

    const globalStack = new GlobalStack(this, 'global', props);
    Tags.of(globalStack).add('StackType', 'Global');

    const propsWithVpc: CoraStackPropsWithVpc = { ...props, vpc: globalStack.vpc };

    const auroraDbStack = new AuroraDatabaseStack(this, 'auroraDb', propsWithVpc);
    Tags.of(auroraDbStack).add('StackType', 'AuroraDatabase');
    auroraDbStack.addDependency(globalStack);

    const processSteeringStack = new ProcessSteeringStack(this, 'process-steering', propsWithVpc);
    Tags.of(processSteeringStack).add('StackType', 'ProcessSteeringStack');
    processSteeringStack.addDependency(auroraDbStack);

    const backendStack = new BackendStack(this, 'backend', propsWithVpc);
    Tags.of(backendStack).add('StackType', 'Backend');
    backendStack.addDependency(processSteeringStack);

    const auditTrailStack = new AuditTrailStack(this, 'audit-trail', propsWithVpc);
    Tags.of(auditTrailStack).add('StackType', 'AuditTrail');
    auditTrailStack.addDependency(auroraDbStack);

    const pvmsDataSyncStack = new PVMSOrderDataSyncStack(this, 'pvms-order-data-sync', propsWithVpc);
    Tags.of(pvmsDataSyncStack).add('StackType', 'PVMSOrderDataSync');
    pvmsDataSyncStack.addDependency(auroraDbStack);

    const cssDataSyncStack = new CSSOrderDataSyncStack(this, 'css-order-data-sync', propsWithVpc);
    Tags.of(pvmsDataSyncStack).add('StackType', 'CSSOrderDataSync');
    cssDataSyncStack.addDependency(auroraDbStack);

    const p06DataSyncStack = new P06OrderDataSyncStack(this, 'p06-order-data-sync', propsWithVpc);
    Tags.of(p06DataSyncStack).add('StackType', 'P06OrderDataSync');
    pvmsDataSyncStack.addDependency(auroraDbStack);

    const frontendStack = new FrontendStack(this, 'frontend', propsWithVpc);
    Tags.of(frontendStack).add('StackType', 'Frontend');
    frontendStack.addDependency(globalStack);

    const bossSyncStack = new BossSyncStack(this, 'bossSync', propsWithVpc);
    Tags.of(bossSyncStack).add('StackType', 'BossSync');
    bossSyncStack.addDependency(auroraDbStack);

    const quotaSyncStack = new QuotaSyncStack(this, 'quotaSync', propsWithVpc);
    Tags.of(quotaSyncStack).add('StackType', 'QuotaSync');
    quotaSyncStack.addDependency(globalStack);

    const modelTypeTextsSyncStack = new ModelTypeTextsSyncStack(this, 'model-type-texts-sync', propsWithVpc);
    Tags.of(modelTypeTextsSyncStack).add('StackType', 'ModelTypeTextsSync');
    modelTypeTextsSyncStack.addDependency(auroraDbStack);

    if (props.stage === 'dev') {
      const e2eTestStack = new E2eTestStack(this, 'e2e-test-stack', {
        ...props,
        testRoleName: 'cora-gitlab-testrunner-role',
      });
      Tags.of(e2eTestStack).add('StackType', 'E2eTestStack');
      e2eTestStack.addDependency(auroraDbStack);
    }

    if (props.disasterRecovery) {
      const disasterRecoveryStack = new DisasterRecoveryStack(this, 'disasterRecovery', propsWithVpc);
      Tags.of(disasterRecoveryStack).add('StackType', 'DisasterRecovery');
      disasterRecoveryStack.addDependency(auroraDbStack);
    }
  }
}
