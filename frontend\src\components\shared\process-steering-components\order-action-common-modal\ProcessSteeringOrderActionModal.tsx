import { PModal } from '@porsche-design-system/components-react';
import {
  InboundApiEventResponse,
  OneVmsEventKey,
} from '../../../../../../infrastructure/lib/types/process-steering-types';
import { CarOrderInList, ModalState, OrderActionType } from '../../../../store/types';
import { useEffect, useState } from 'react';
import { OrderActionPreviewStateComponent } from '../../order-action-common-modal/OrderActionPreviewStateComponent';
import { ProcessSteeringResultStateComponent } from './ProcessSteeringResultState';

export interface ProcessSteeringModalProps {
  orders: CarOrderInList[];
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  error?: any;
  result?: InboundApiEventResponse;
  actionType: OrderActionType | OneVmsEventKey;
  closeModal: (state: ModalState) => void;
  handleSubmit: () => void;
  CustomComponent?: React.FC;
  isSubmitDisabled?: boolean;
  subFormError?: string;
}

const ProcessSteeringOrderActionModal: React.FC<ProcessSteeringModalProps> = (props) => {
  const [modalState, setModalState] = useState<ModalState>('preview');
  const isMulti = props.orders.length > 1;

  useEffect(() => {
    if (props.isSuccess || props.isError) {
      setModalState('result');
    }
  }, [props.isSuccess, props.isError]);

  const handleClose = () => props.closeModal(modalState);

  return (
    <PModal
      disableBackdropClick
      data-e2e={`${props.actionType}_order_modal`}
      open
      dismissButton={false}
      onDismiss={handleClose}
    >
      {modalState === 'preview' ? (
        <OrderActionPreviewStateComponent
          ordersToHandle={props.orders}
          isMulti={isMulti}
          handleSubmit={props.handleSubmit}
          closeModal={handleClose}
          CustomComponent={props.CustomComponent}
          isLoading={props.isLoading}
          actionType={props.actionType}
          isSubmitDisabled={props.isSubmitDisabled}
          subFormError={props.subFormError}
        />
      ) : (
        <ProcessSteeringResultStateComponent
          orders={props.orders}
          isMulti={isMulti}
          closeModal={handleClose}
          error={props.error}
          result={props.result}
          actionType={props.actionType as OneVmsEventKey}
        />
      )}
    </PModal>
  );
};

export default ProcessSteeringOrderActionModal;
