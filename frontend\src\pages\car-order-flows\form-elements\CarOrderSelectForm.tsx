import { <PERSON>electWrap<PERSON>, PSpinner } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetCarOrdersForDealerQuery } from '../../../store/api/NewCarOrderApi';
import { FetchError } from '../../errors/FetchErrors';
import { CarOrderInList } from '../../../store/types';

interface CarOrderSelectFormProps {
  dealer_number: string;
  selectedOrderId?: string;
  handleSelect: (orderId: string) => void;
  setError: (area: string, err?: string) => void;
  setHasAccess?: (access: boolean) => void;
  disableButton?: boolean | false;
}

export const CarOrderSelectForm: React.FC<CarOrderSelectFormProps> = ({
  carOrders,
  selectedOrderId,
  handleSelect,
  disableDropdown,
}) => {
  const { t } = useTranslation();

  return (
    <div style={{ flexBasis: 'calc(50% - 10px)' }}>
      <label htmlFor="car-order-select">{t('select_existing_order')}</label>
      <select
        id="car-order-select"
        value={selectedOrderId ?? ''}
        disabled={disableDropdown || carOrders.length === 0}
        onChange={(e) => handleSelect(e.target.value)}
        required
        style={{ width: '100%', padding: '0.5rem' }}
      >
        <option value="">{t('choose_order_prompt')}</option>
        {carOrders.map((order) => (
          <option key={order.pk_new_car_order_id} value={order.pk_new_car_order_id}>
            {`${order.pk_new_car_order_id} | ${order.quota_month ?? 'no quota'} | ${order.order_type}`}
          </option>
        ))}
      </select>
    </div>
  );
};
