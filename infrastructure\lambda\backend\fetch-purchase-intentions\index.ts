import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Kas<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { ModelTypeVisibilityModel } from '../../../lib/entities/model-type-visibility-model';
import { CoraPurchaseIntentionModel } from '../../../lib/entities/purchase-intention-model';
import { CoraNCPurchaseIntentionRequestBody } from '../../../lib/types/purchase-intention-types';
import { Constants } from '../../../lib/utils/constants';
import { ObjectValidator } from '../../../lib/utils/object-validation';
import { createTypeORMDataSource } from '../../config/typeorm-config';
import { createApiGwHandler } from '../../utils/api-gw-handler';
import { getAuthContext, sanitizeApiGwEvent, sendFail, sendSuccess } from '../../utils/api-helpers';
import { secretCache } from '../../utils/secret-cache';
import { getEnvVarWithAssert } from '../../utils/utils';
import { getAllowedDealers } from '../../utils/validation-helpers';
import { agGridSsrmCreateWhereSqlForPurchaseIntentions } from './query-utils';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const stage = getEnvVarWithAssert('STAGE');
const objectValidator = new ObjectValidator<CoraNCPurchaseIntentionRequestBody>('CoraNCPurchaseIntentionRequestBody');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
let columnsToSelect: string[] = [];
secretCache.initCache(AURORA_SECRET_ARN);

const fetchPurchaseIntentionFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  logger.log(LogLevel.DEBUG, 'Fetching purchase intentions for dealer', {
    data: sanitizeApiGwEvent({ event }, logger),
  });
  if (!event.body) {
    return sendFail(
      {
        message: 'Invalid Queryparameters. No request body',
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  let queryParameters: unknown = {};
  try {
    queryParameters = JSON.parse(event.body) ?? {};
  } catch (error) {
    logger.log(LogLevel.WARN, 'invalid event body', { data: error });
    return sendFail(
      {
        message: 'Invalid Event Body',
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  logger.log(LogLevel.DEBUG, 'queryParameters', { data: queryParameters });
  const params = queryParameters as CoraNCPurchaseIntentionRequestBody;

  logger.log(LogLevel.DEBUG, 'Received filter keys', {
    data: Object.keys(params.filterModel ?? {}),
  });

  const [validatedQueryParams, validation_errors] = objectValidator.validate(queryParameters);
  if (validatedQueryParams === null) {
    logger.log(LogLevel.WARN, 'ajv validation failed', { data: validation_errors });
    return sendFail(
      {
        message: 'Invalid Queryparameters. Validation failed with: ' + JSON.stringify(validation_errors),
        status: 400,
        reqHeaders: event.headers,
      },
      logger,
    );
  }
  logger.log(LogLevel.DEBUG, 'Validated filters', { data: validatedQueryParams });

  const allowedDealers = await getAllowedDealers({ dynamoDb: dynamoDb, event }, logger);

  const userAttributes = getAuthContext({ event }, logger);

  //look at the first entry for the cora application and take the visibility from there
  const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
  if (!visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the visibility level', { data: sanitizeApiGwEvent({ event }, logger) });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  if (allowedDealers.length === 0) {
    return sendFail(
      { message: 'User is not authorized an any dealers', status: 403, reqHeaders: event.headers },
      logger,
    );
  }

  const allDealerNrs = allowedDealers.map((rel) => rel.dealer_number).filter((dealNr) => dealNr !== undefined);
  const offset = validatedQueryParams.startRow ?? 0;
  const limit = validatedQueryParams.endRow ? validatedQueryParams.endRow - offset : 100;
  const purchaseIntentions: CoraPurchaseIntentionModel[] = [];

  //perform rds query to get all relevant purchase intentions
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [
      CoraPurchaseIntentionModel,
      ModelTypeVisibilityModel,
    ]);

    const queryBuilder = dataSource.getRepository(CoraPurchaseIntentionModel).createQueryBuilder('pi');
    // Select specific columns (optional)
    if (columnsToSelect.length === 0) {
      columnsToSelect = queryBuilder.connection
        .getMetadata(CoraPurchaseIntentionModel)
        .columns.map((col) => `pi.${col.propertyName}`);

      logger.log(LogLevel.DEBUG, 'columns to get', { data: columnsToSelect });
    }
    queryBuilder.select(columnsToSelect);
    // Filter by allowed dealers
    queryBuilder.where('pi.dealer_number IN (:...dealerNumbers)', { dealerNumbers: allDealerNrs });
    queryBuilder.andWhere('pi.is_converted = :isConverted', { isConverted: false });
    // Status filter (hardcoded enum or constant)
    queryBuilder.andWhere('pi.vehicle_status_code IN (:...statusCodes)', {
      statusCodes: Constants.PVMS_PURCHASE_INTENTION_STATUS_FOR_LIST,
    });
    // ag-Grid filterModel (SSRM)
    if (validatedQueryParams.filterModel) {
      agGridSsrmCreateWhereSqlForPurchaseIntentions(queryBuilder, validatedQueryParams.filterModel, logger);
      logger.log(LogLevel.DEBUG, 'QueryBuilder agGrid filters applied');
    }
    // ModelTypeVisibility Join
    const mtv_from = new Date().toISOString().split('T', 1)[0];
    queryBuilder.innerJoin(
      ModelTypeVisibilityModel,
      'mtv',
      'pi.cnr = mtv.cnr AND pi.model_year = mtv.my4 AND pi.importer_number = mtv.importer_number AND pi.model_type = mtv.model_type',
    );
    queryBuilder.andWhere('mtv.role = :role', { role: visibilityLevel });
    queryBuilder.andWhere('mtv.valid_from <= :valid_min', { valid_min: mtv_from });

    // Pagination
    queryBuilder.limit(limit);
    queryBuilder.offset(offset);
    // Sorting
    if (validatedQueryParams.sortModel && validatedQueryParams.sortModel.length > 0) {
      queryBuilder.orderBy(
        `pi.${validatedQueryParams.sortModel[0].colId}`,
        validatedQueryParams.sortModel[0].sort === 'desc' ? 'DESC' : 'ASC',
      );
    }

    const query = queryBuilder.getQuery();

    logger.log(LogLevel.DEBUG, 'Final Purchase Intention SQL', {
      data: {
        query,
        parameters: params,
      },
    });

    const data = await queryBuilder.getMany();
    purchaseIntentions.push(...data);

    logger.log(LogLevel.DEBUG, `Got db response for Purchase Intentions`, { data: purchaseIntentions.length });
  } catch (error) {
    logger.log(LogLevel.ERROR, `Failed to get Purchase Intentions from RDS`, { data: error });
    return sendFail(
      {
        message: `Internal Server Error, CorrelationId: ${logger.getCorrelationId()}`,
        status: 500,
        reqHeaders: event.headers,
      },
      logger,
    );
  }

  logger.log(LogLevel.DEBUG, `Retrieved totally ${purchaseIntentions.length} purchase intentions by Querying`);

  //add dealer name to result
  const purchaseIntentionsWithDealerName = purchaseIntentions.map((pi) => ({
    ...pi,
    dealer_name: allowedDealers.find((dlr) => dlr.dealer_number === pi.dealer_number)?.display_name,
  }));

  return sendSuccess(
    { body: { data: purchaseIntentionsWithDealerName, rowCount: -1 }, reqHeaders: event.headers },
    logger,
  );
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-purchase-intentions', LogLevel.TRACE)(event, context, fetchPurchaseIntentionFunc);
