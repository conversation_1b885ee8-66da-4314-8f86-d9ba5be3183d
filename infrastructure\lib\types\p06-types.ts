// Main DTO interface
export interface P06NewCarOrderDataDTO {
  ids: P06IDs;
  model_info: P06ModelInfo;
  order_info: P06OrderInfo;
  logistics_info: P06LogisticsInfo;
  production_info?: P06ProductionInfo;
  appointment_date_info?: P06AppointmentDateInfo;
  vehicle_technical_info?: P06VehicleTechnicalInfo;
  resolved_config_options?: P06ResolvedConfigOption[];
}

// Kafka Wrapper
export interface P06NewCarOrderKafkaObject {
  key: string;
  value: P06NewCarOrderDataDTO;
  timestamp: number;
}

export interface P06IDs {
  new_car_order_id: string;
  vguid_piaom_DEPRECATED?: string;
  internal_vehicle_number_piaom_DEPRECATED?: string;
  commission_id?: string;
  production_number_porsche?: string;
  production_number_vw?: string;
  vehicle_identification_number?: string;
  kate_id?: string;
  twikit_id?: string;
  business_partner_id?: string;
}

export interface P06ModelInfo {
  model_year_code?: string;
  model_year: number;
  material_sap_DEPRECATED?: string;
  transmission_type?: string;
  country_code_deviation_for_vin_determination?: string;
}

export interface P06OrderInfo {
  base_info: {
    quota_month: string;
    order_type: string;
    is_locator_visible?: boolean;
  };
  trading_partner: {
    importer_code: string;
    importer_number: string;
    dealer_ship_to_number: string;
  };
  status_info: {
    vehicle_status_piaom_code: string;
    vehicle_status_piaom_timestamp: string;
    order_status_piaom_code: string;
    order_status_piaom_timestamp: string;
  };
  sales_info?: {
    cancellation_reason?: string;
  };
  planning?: {
    has_checked_quota: boolean;
    lead_time_days: number;
    material_lead_time_days?: string;
    order_date_erwin?: string;
    has_change_lock?: boolean;
    is_order_edited?: boolean;
    pickup_plant_code?: string;
    planning_plant_code?: string;
    has_twikit?: boolean;
    has_lena?: boolean;
    has_exclusive_option?: boolean;
    has_custom_tailoring_option?: boolean;
    has_custom_tailoring_cxx?: boolean;
    has_cxx_price_maintained?: boolean;
    has_z_request?: boolean;
    has_control_z_request?: boolean;
    has_cop?: boolean;
    is_vip?: boolean;
    is_pos_mandatory?: boolean;
    kfz_brief_print?: string;
    has_emission_determination_error?: boolean;
    lead_time_cp00_cp80?: number;
    manual_cp80_offset?: number;
    is_ontop_manual_cp80_offset?: boolean;
    custom_tailoring_installation_days?: number;
    has_fixed_production_schedule?: boolean;
    is_fixed_in_production_schedule?: boolean;
    is_order_fixed?: boolean;
    first_schedule_order_book_date?: string;
    orientation_day_dial_date?: string;
    slotting_date?: string;
    configuration_validity_start_date?: string;
    configuration_validity_end_date?: string;
    feasibility_check_date?: string;
    last_change_distribution_data_timestamp?: string;
    last_change_configuration_data_timestamp?: string;
    price_date?: string;
    blocking_reason_planning?: string;
    icc_id?: string;
  };
  delivery_info?: {
    mileage?: number;
    mileage_unit?: string;
  };
}

export interface P06LogisticsInfo {
  shipping_code: string;
  shipping_block?: string;
  transfer_of_risk_location_porsche_code?: string;
  transfer_of_risk_location_vw_code?: string;
  current_location_code?: string;
  next_location_code?: string;
  receiving_port_code?: string;
  vessel_imo_number?: string;
}

export interface P06ProductionInfo {
  production_plant?: string;
}

export interface P06AppointmentDateInfo {
  production_logistic_dates?: {
    order_creation_date?: string;
    change_freeze_point_date?: string;
    planned_release_to_production_date?: string;
    actual_release_to_production_date?: string;
    expected_cp_00_date?: string;
    actual_cp_00_date?: string;
    expected_cp_22_date?: string;
    expected_cp_78_date_planning?: string;
    expected_cp_80_date?: string;
    expected_cp_80_date_vin?: string;
    actual_dealer_delivery_date?: string;
  };
  process_specific?: {
    factory_pickup_status?: string;
  };
}

export interface P06VehicleTechnicalInfo {
  general?: {
    combustion_engine_number?: string;
    electric_engine_number?: string;
    tire_code?: string;
    key_code?: string;
    emission_standard?: string;
  };
}

export interface P06ResolvedConfigOption {
  option_code: string;
  option_id: string;
  option_type_short?: string;
  option_type_long?: string;
  reference_package?: string;
  option_class?: string;
  option_class_detail?: string;
  z_request_colour?: string;
  custom_tailoring_text?: string;
}
