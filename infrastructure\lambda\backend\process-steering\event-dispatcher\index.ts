import { Context, SQSEvent, <PERSON>Q<PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { SQSClient } from '@aws-sdk/client-sqs';
import { Kas<PERSON><PERSON>bdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { v4 as uuidv4 } from 'uuid';
import { getEnvVarWithAssert, pushNotificationsToKafka } from '../../../utils/utils';
import { OneVmsEventHandlerKey } from '../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import {
  BatchItemFailureWithError,
  InboundEventDispatcherEvent,
  InboundEventHandlerEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  SpecialStatusCode,
  SQSBatchResponseWithError,
} from '../../../../lib/types/process-steering-types';
import { createSqsEventHandlerWithInitLogger } from '../../../utils/sqs-event-handler';
import { secretCache } from '../../../utils/secret-cache';
import { In, Repository } from 'typeorm';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../lib/entities/new-car-order-model';
import { inboundMappingCache } from '../../../utils/eventhandler-inbound-mapping-cache';
import { InboundProcessMappingModel } from '../../../../lib/entities/inbound-mapping-model';
import { InboundEventDispatcherError } from '../../../../lib/types/custom-error-types';
import { KafkaAdapter } from '../../../utils/kafka';
import { dispatchToSqsQueue } from '../../../utils/process-steering-helpers';
import { OneVmsStatusModel } from '../../../../lib/entities/onevms-status-model';
import { validateSqsEvent } from '../../../utils/validation-helpers';

// Environment Configuration
const EVENT_HANDLER_QUEUES = JSON.parse(getEnvVarWithAssert('EVENT_HANDLER_QUEUES')) as Record<
  OneVmsEventHandlerKey,
  string
>;
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
const STAGE = getEnvVarWithAssert('STAGE');
const KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
const KAFKA_SECRET_ARN = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];

const kasLambdaLogger = new KasLambdaLogger('EventDispatcher', LogLevel.TRACE);

// External Dependencies
const sqsClient = new SQSClient({ tls: true });
const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: KAFKA_SECRET_ARN,
  logger: kasLambdaLogger,
});

const inboundEventDispatcherEventValidator = new ObjectValidator<InboundEventDispatcherEvent>(
  'InboundEventDispatcherEvent',
);
secretCache.initCache(KAFKA_SECRET_ARN, AURORA_SECRET_ARN);

/**
 * Event Dispatcher function:
 *
 * Receives batched SQS events and dispatches valid sub-events to the correct downstream event handler queues.
 * Also publishes notification events for dispatched and non dispatched events.
 * Non dispatched events, due to any reason, are returned and send to the DLQ.
 * Possible reasons for non dispatched events could be:
 *  - Issues with database connections
 *  - Parsing and validation errors
 *  - Existing NCO not found
 *  - Racing conditions
 *  - Event handler mapping invalid
 *  - Event handler does not exist (yet)
 *  - Issues while dispatching events to eventhandler queues
 *
 * --- Main Functionalities ---
 * 1. Parse and validate all incoming SQS events
 * 2. Load referenced NCOs using a single DB lookup
 * 3. Check if mapping for the current event + NCO status exists (with racing condition check)
 * 4. Dispatch valid events per resolved handler queue (grouped & batched)
 * 5. Track all dispatch failures to be returned in batch response and DLQ'ed
 * 6. Publish success/error notifications for each processed event
 */
const eventDispatcherFunc = async (
  sqsEvent: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponseWithError> => {
  let errorMessage: string;
  logger.log(LogLevel.INFO, `Got SQS event, processing...`, { data: sqsEvent.Records });

  const batchItemFailures: BatchItemFailureWithError[] = [];
  const dispatchEvents: { handlerKey: OneVmsEventHandlerKey; event: InboundEventHandlerEvent }[] = [];
  const messageIdBySubTransaction = new Map<string, string>();

  let ncoRepo: Repository<NewCarOrderModel>;

  // --- Init TypeORM data source and inbound mapping cache ---
  try {
    const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, STAGE, [
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      InboundProcessMappingModel,
      OneVmsStatusModel,
    ]);
    await inboundMappingCache.init(dataSource, logger);
    ncoRepo = dataSource.getRepository(NewCarOrderModel);
  } catch (error) {
    errorMessage = 'Error initializing datasource or inbound mapping cache';
    logger.log(LogLevel.ERROR, `${errorMessage}. Aborting all events.`, { data: error });
    return {
      batchItemFailures: sqsEvent.Records.map((record) => ({ itemIdentifier: record.messageId, errorMessage })),
    };
  }

  // --- Step 1: Parse and validate incoming SQS records ---
  const parsedEvents = sqsEvent.Records.map((record) => {
    try {
      const inboundEvent = JSON.parse(record.body) as InboundEventDispatcherEvent;
      const isValid = validateSqsEvent<InboundEventDispatcherEvent>(
        inboundEvent,
        inboundEventDispatcherEventValidator,
        logger,
      );
      if (!isValid)
        throw new InboundEventDispatcherError('Validation failed', inboundEvent, inboundEvent.transaction_id);
      return { event: inboundEvent, originalMessageId: record.messageId };
    } catch (error) {
      errorMessage = 'Failed to parse/validate SQS record';
      logger.log(LogLevel.WARN, errorMessage, { data: { error } });
      batchItemFailures.push({ itemIdentifier: record.messageId, errorMessage });
      return undefined;
    }
  }).filter(Boolean) as { event: InboundEventDispatcherEvent; originalMessageId: string }[];

  // --- Step 2: Bulk load referenced NCOs ---
  const allNcoIds = parsedEvents.flatMap(
    (parsedEvent) => parsedEvent.event.ncos_info?.map((nco) => nco.pk_new_car_order_id) ?? [],
  );
  const ncoMap = new Map<string, NewCarOrderModel>();
  if (allNcoIds.length > 0) {
    const ncos = await ncoRepo.findBy({ pk_new_car_order_id: In(allNcoIds) });
    ncos.forEach((nco) => ncoMap.set(nco.pk_new_car_order_id, nco));
    logger.log(LogLevel.DEBUG, 'Fetched all existing NCOs from Database.', {
      data: { amountOfNewCarOrders: ncos.length, allNcoIds },
    });
  }

  // --- Step 3: Build handler events + notifications ---
  for (const { event: dispatcherEvent, originalMessageId } of parsedEvents) {
    const baseNotification = {
      transaction_id: dispatcherEvent.transaction_id,
      event_type: dispatcherEvent.event_type,
      action_by: dispatcherEvent.user_auth_context
        ? dispatcherEvent.user_auth_context.username
        : dispatcherEvent.source_system,
      source_system: dispatcherEvent.source_system,
    };

    const ncoEntries = dispatcherEvent.ncos_info ?? [undefined];

    for (const nco of ncoEntries) {
      const subTransactionId = nco?.sub_transaction_id ?? dispatcherEvent.sub_transaction_id ?? uuidv4();
      const ncoId = nco?.pk_new_car_order_id;
      const modifiedAtInput = nco?.modified_at;
      const ncoFromDb = ncoId ? ncoMap.get(ncoId) : undefined;

      let handlerKey: OneVmsEventHandlerKey | undefined;
      let errorReason: string | undefined;

      let modifiedAtDb = undefined;

      if (ncoFromDb?.modified_at) {
        modifiedAtDb = new Date(ncoFromDb.modified_at).toISOString();
      }

      // Catch errors/issues and handle accordingly
      if (ncoId && modifiedAtInput && !ncoFromDb) {
        errorReason = 'Referenced NCO not found in database';
        logger.log(LogLevel.WARN, errorReason);
      } else if (ncoId && modifiedAtInput && modifiedAtDb && modifiedAtDb !== modifiedAtInput) {
        errorReason = 'Racing condition occurred';
        logger.log(LogLevel.WARN, errorReason, { data: { modifiedAtDb, modifiedAtInput } });
      } else {
        handlerKey = inboundMappingCache.getHandlerKey(
          {
            order_status_code: ncoFromDb?.order_status_onevms_code ?? SpecialStatusCode.NONE,
            error_status_code: ncoFromDb?.order_status_onevms_error_code ?? SpecialStatusCode.NONE,
            invoice_status_code: ncoFromDb?.order_invoice_onevms_code ?? SpecialStatusCode.NONE,
            event: dispatcherEvent.event_type,
            source_system: dispatcherEvent.source_system,
          },
          logger,
        );

        if (!handlerKey) {
          errorReason = 'No mapping found for event. This transaction cannot be fulfilled';
          logger.log(LogLevel.WARN, errorReason);
        } else if (!EVENT_HANDLER_QUEUES[handlerKey]) {
          errorReason = 'Event handler queue not defined';
          logger.log(LogLevel.ERROR, errorReason);
        }
      }

      // If any issues/errors occured, publish them to the notification topic and push to failed dispatch events.
      if (errorReason) {
        batchItemFailures.push({ itemIdentifier: originalMessageId, errorMessage: errorReason });
        await pushNotificationsToKafka(
          [
            {
              ...baseNotification,
              action_at: new Date().toISOString(),
              sub_transaction_id: subTransactionId,
              nco_id: ncoId,
              status: NotificationStatus.ERROR,
              details: { error: errorReason },
            },
          ],
          KAFKA_TOPIC_NOTIFICATION,
          kafkaAdapter,
          logger,
        );
        continue;
      }

      // Prepare and store dispatch events for export in eventhandler SQS queue
      const handlerEvent: InboundEventHandlerEvent = {
        event_type: dispatcherEvent.event_type,
        transaction_id: dispatcherEvent.transaction_id,
        sub_transaction_id: subTransactionId,
        action_at: new Date().toISOString(),
        user_auth_context: dispatcherEvent.user_auth_context,
        payload: dispatcherEvent.payload,
        source_system: dispatcherEvent.source_system,
        nco_id: ncoId,
        modified_at: modifiedAtInput,
      };

      dispatchEvents.push({ handlerKey: handlerKey!, event: handlerEvent });
      messageIdBySubTransaction.set(subTransactionId, originalMessageId);
    }
  }

  logger.log(LogLevel.DEBUG, 'Dispatching all valid events to their respective queues.');

  // --- Step 4: Dispatch validated events to event handler SQS queues in batches ---
  const failures = await dispatchToSqsQueue(
    dispatchEvents,
    messageIdBySubTransaction,
    EVENT_HANDLER_QUEUES,
    sqsClient,
    logger,
  );
  batchItemFailures.push(...failures);

  // --- Step 5: Publish success notifications ---
  for (const { event } of dispatchEvents) {
    const sourceMessageId = messageIdBySubTransaction.get(event.sub_transaction_id);
    const wasDispatched = !batchItemFailures.find((f) => f.itemIdentifier === sourceMessageId);

    await pushNotificationsToKafka(
      [
        eventToNotification(
          event,
          wasDispatched ? NotificationStatus.DISPATCHED : NotificationStatus.ERROR,
          wasDispatched
            ? { status: 'Request was dispatched. Wait for event handler' }
            : { error: 'SQS dispatch failed' },
        ),
      ],
      KAFKA_TOPIC_NOTIFICATION,
      kafkaAdapter,
      logger,
    );
  }

  logger.log(LogLevel.INFO, `Dispatching finished for this transaction.`, {
    data: {
      successCount: Math.max(dispatchEvents.length - batchItemFailures.length, 0),
      failedCount: batchItemFailures.length,
    },
  });
  return { batchItemFailures };
};

function eventToNotification(
  event: InboundEventHandlerEvent,
  status: NotificationStatus,
  details?: unknown,
): NotificationKafkaEvent {
  return {
    transaction_id: event.transaction_id,
    sub_transaction_id: event.sub_transaction_id,
    event_type: event.event_type,
    source_system: event.source_system,
    nco_id: event.nco_id,
    action_at: event.action_at,
    action_by: event.user_auth_context?.username ?? 'unknown',
    status,
    details,
  };
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(kasLambdaLogger)(event, context, eventDispatcherFunc);
