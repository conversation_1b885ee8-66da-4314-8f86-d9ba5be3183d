import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getApiDomain } from '../../services/ServiceUtils';
import { RootState } from '../configureStore';

export const baseApi = createApi({
  tagTypes: ['piList', 'ncoList', 'currentNco', 'currentPi', 'quota', 'auditNco', 'carOrdersList'],
  baseQuery: fetchBaseQuery({
    baseUrl: `${getApiDomain()}`,
    headers: { 'Content-Type': 'application/json' },
    prepareHeaders: (headers, { getState }) => {
      const state = getState() as RootState;
      const correlationId = state.correlation.correlationId;
      if (correlationId) {
        headers.set('x-kas-request-id', correlationId);
      }

      return headers;
    },
    credentials: 'include',
    timeout: 60000, // increase to 1 minute for cancel action
  }),
  endpoints: (builder) => ({}),
});
