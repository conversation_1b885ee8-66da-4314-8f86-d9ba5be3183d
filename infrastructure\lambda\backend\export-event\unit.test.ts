/* eslint-disable @typescript-eslint/no-explicit-any*/
/* eslint-disable @typescript-eslint/no-unsafe-member-access*/

import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { auroraUnitTestSecret, dummyKafkaSecret, mockContext, setupMocks } from '../../utils/test-utils';
import { KafkaAdapter } from '../../utils/kafka';
import {
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
} from '../../../lib/types/process-steering-types';
import { v4 as uuidv4 } from 'uuid';
import newCarOrderAuditTrailModelsJson from '../../../test/data/new-car-order-audit-models.json';

const mocks = setupMocks();
const pushObjsToTopicMock = jest.fn();
jest.mock('../../utils/kafka');
(KafkaAdapter as jest.MockedClass<typeof KafkaAdapter>).mockImplementation(() => {
  return {
    pushObjsToTopic: pushObjsToTopicMock,
  } as unknown as KafkaAdapter;
});

(KafkaAdapter as jest.MockedClass<typeof KafkaAdapter>).mockImplementation(() => {
  return {
    pushObjsToTopic: pushObjsToTopicMock,
  } as unknown as KafkaAdapter;
});

import { handler } from './index';
import { KafkaObjsWrapper, KafkaObjTyp } from '../export-nco/types';
import { MSKEvent } from 'aws-lambda';

beforeEach(() => {
  mocks.ddbMock!.reset();
  mocks.smMock!.reset();
  jest.resetModules();
  pushObjsToTopicMock.mockReset();
});

describe('Export notification event lambda', () => {
  beforeEach(() => {
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.KAFKA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(dummyKafkaSecret) });
    mocks
      .smMock!.on(GetSecretValueCommand, { SecretId: process.env.AURORA_SECRET_ARN })
      .resolves({ SecretString: JSON.stringify(auroraUnitTestSecret) });
  });

  const buildKafkaEvent = (
    events: Partial<NotificationKafkaEvent>[],
    ce_type = `${KafkaObjTyp.NOTIFICATION}.${NotificationStatus.EVENT_HANDLER_IO}`,
  ): MSKEvent => {
    return {
      eventSource: 'aws:kafka',
      eventSourceArn: 'aws:lambda:dummy:arn',
      bootstrapServers: 'localhost:9092',
      records: {
        KAFKA_TOPIC_NOTIFICATION: events.map((event, index) => ({
          topic: 'FRA_one_vms_cora_notifications_new_car_order_DEV',
          key: Buffer.from('key').toString('base64'),
          partition: 0,
          offset: index,
          value: Buffer.from(JSON.stringify(event)).toString('base64'),
          timestamp: Date.now(),
          timestampType: 'CREATE_TIME',
          headers: [
            {
              ce_type: Array.from(Buffer.from(ce_type)),
            },
          ],
        })),
      },
    };
  };
  const event: NotificationKafkaEvent = {
    transaction_id: uuidv4(),
    sub_transaction_id: uuidv4(),
    event_type: OneVmsEventKey.UPDATE_CORE_DATA,
    nco_id: newCarOrderAuditTrailModelsJson[0].pk_new_car_order_id,
    action_by: 'tester',
    action_at: new Date().toISOString(),
    source_system: OneVmsSourceSystemKey.CORA_USER,
    status: NotificationStatus.EVENT_HANDLER_IO,
  };

  it('Completely successful export', async () => {
    await handler(buildKafkaEvent([event]), mockContext, () => {});
    expect(pushObjsToTopicMock).toHaveBeenCalled();
    const calls = pushObjsToTopicMock.mock.calls.flat() as {
      topic: string;
      kWrapper: KafkaObjsWrapper<any>;
      correlationid: string;
    }[];

    const pvmsCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_PVMS);
    const oneVmsCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_ONE_VMS);
    const p06Call = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_P06);
    const notiCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_NOTIFICATION);
    expect(pvmsCall?.kWrapper.kafkaObjs[0].id).toEqual(event.nco_id);
    expect(oneVmsCall?.kWrapper.kafkaObjs[0].id).toEqual(event.nco_id);
    expect(p06Call?.kWrapper.kafkaObjs[0].id).toEqual(event.nco_id);
    expect(notiCall?.kWrapper.kafkaObjs[0].obj.nco_id).toEqual(event.nco_id);
    expect(notiCall?.kWrapper.kafkaObjs[0].obj.status).toEqual('exported');
  });

  it('Exports notification event to OneVMS topic. Should not be exported to PVMS or P06 topic.', async () => {
    const auditTrail = newCarOrderAuditTrailModelsJson[0];
    newCarOrderAuditTrailModelsJson[0].action_type = 'core_data.updated';

    await handler(buildKafkaEvent([event]), mockContext, () => {});
    expect(pushObjsToTopicMock).toHaveBeenCalled();
    const calls = pushObjsToTopicMock.mock.calls.flat() as {
      topic: string;
      kWrapper: KafkaObjsWrapper<any>;
      correlationid: string;
    }[];

    const pvmsCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_PVMS);
    const oneVmsCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_ONE_VMS);
    const p06Call = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_P06);
    const notiCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_NOTIFICATION);
    expect(pvmsCall).toBeUndefined();
    expect(p06Call).toBeUndefined();
    expect(oneVmsCall?.kWrapper.kafkaObjs[0].id).toEqual(event.nco_id);
    expect(notiCall?.kWrapper.kafkaObjs[0].obj.nco_id).toEqual(event.nco_id);
    expect(notiCall?.kWrapper.kafkaObjs[0].obj.status).toEqual('exported');
    // Reset test data
    newCarOrderAuditTrailModelsJson[0] = auditTrail;
  });

  it('Skips this NotificationEvent when ce_type is not event_handler_io', async () => {
    const kafkaEvent = buildKafkaEvent([event], `${KafkaObjTyp.NOTIFICATION}.${NotificationStatus.EVENT_HANDLER_NIO}`);
    await handler(kafkaEvent, mockContext, () => {});
    expect(pushObjsToTopicMock).not.toHaveBeenCalled();
  });

  it('Skips event when nco_id is missing', async () => {
    const eventWithoutNcoId: NotificationKafkaEvent = {
      transaction_id: uuidv4(),
      sub_transaction_id: uuidv4(),
      event_type: OneVmsEventKey.UPDATE_CORE_DATA,
      action_by: 'tester',
      action_at: new Date().toISOString(),
      source_system: OneVmsSourceSystemKey.CORA_USER,
      status: NotificationStatus.EVENT_HANDLER_IO,
    };

    await handler(buildKafkaEvent([eventWithoutNcoId]), mockContext, () => {});
    expect(pushObjsToTopicMock).not.toHaveBeenCalled();
  });

  it('Skips record with no value (empty payload)', async () => {
    const kafkaEvent: MSKEvent = {
      eventSource: 'aws:kafka',
      eventSourceArn: 'dummy',
      bootstrapServers: 'localhost:9092',
      records: {
        KAFKA_TOPIC_NOTIFICATION: [
          {
            topic: 'FRA_one_vms_cora_notifications_new_car_order_DEV',
            key: Buffer.from('key').toString('base64'),
            partition: 0,
            offset: 0,
            value: '',
            timestamp: Date.now(),
            timestampType: 'CREATE_TIME',
            headers: [
              {
                ce_type: Array.from(Buffer.from(`${KafkaObjTyp.NOTIFICATION}.${NotificationStatus.EVENT_HANDLER_IO}`)),
              },
            ],
          },
        ],
      },
    };

    await handler(kafkaEvent, mockContext, () => {});
    expect(pushObjsToTopicMock).not.toHaveBeenCalled();
  });

  it('Skips record with invalid JSON', async () => {
    const kafkaEvent: MSKEvent = {
      eventSource: 'aws:kafka',
      eventSourceArn: 'dummy',
      bootstrapServers: 'localhost:9092',
      records: {
        KAFKA_TOPIC_NOTIFICATION: [
          {
            topic: 'FRA_one_vms_cora_notifications_new_car_order_DEV',
            key: Buffer.from('key').toString('base64'),
            partition: 0,
            offset: 0,
            value: Buffer.from('not-a-json').toString('base64'),
            timestamp: Date.now(),
            timestampType: 'CREATE_TIME',
            headers: [
              {
                ce_type: Array.from(Buffer.from(`${KafkaObjTyp.NOTIFICATION}.${NotificationStatus.EVENT_HANDLER_IO}`)),
              },
            ],
          },
        ],
      },
    };

    await handler(kafkaEvent, mockContext, () => {});
    expect(pushObjsToTopicMock).not.toHaveBeenCalled();
  });

  it('Should not export anything due to empty audit trails', async () => {
    const auditTrail = newCarOrderAuditTrailModelsJson[0];
    newCarOrderAuditTrailModelsJson.length = 0;
    await handler(buildKafkaEvent([event]), mockContext, () => {});
    expect(pushObjsToTopicMock).not.toHaveBeenCalled();
    newCarOrderAuditTrailModelsJson[0] = auditTrail;
  });

  it('Should export an error notification for mocked error.', async () => {
    pushObjsToTopicMock.mockImplementationOnce(() => {
      throw new Error('Kafka send failed');
    });

    await handler(buildKafkaEvent([event]), mockContext, () => {});

    const calls = pushObjsToTopicMock.mock.calls.flat() as {
      topic: string;
      kWrapper: KafkaObjsWrapper<NotificationKafkaEvent>;
      correlationid: string;
    }[];

    const errorNotiCall = calls.find((c) => c.topic === process.env.KAFKA_TOPIC_NOTIFICATION);
    expect(errorNotiCall?.kWrapper.kafkaObjs[0].obj.status).toBe('error');
    expect(errorNotiCall?.kWrapper.kafkaObjs[0].obj.details).toHaveProperty('error');
  });
});
