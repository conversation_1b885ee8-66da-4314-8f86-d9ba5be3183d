import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>s<PERSON><PERSON><PERSON>dbT<PERSON>, KasKmsKey, Kas<PERSON><PERSON><PERSON> } from '@kas-resources/constructs';
import { aws_ssm, aws_ec2 as ec2, Stack } from 'aws-cdk-lib';
import { AuthenticationMethod } from 'aws-cdk-lib/aws-lambda-event-sources';
import { Construct } from 'constructs';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk } from '../utils/constants_cdk';
import { GlobalStack } from './global-stack';

export class QuotaSyncStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    //import global table key
    const coraGlobalDynamoKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalDynamoKmsKeyArnPName,
    );
    const coraGlobalDynamoKmsKey = KasKmsKey.fromKeyArn(this, 'globalDynamoKmsKey', coraGlobalDynamoKmsKeyArn);

    // Import global Secret Key and Secrets
    const globalSecretKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.globalSecretKmsKeyArnPName,
    );
    const globalSecretKey = KasKmsKey.fromKeyArn(this, 'globalSecretKey', globalSecretKeyArn);

    //Import Kafka secret for normal env
    const kafkaSecretArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecretArnPName,
    );
    const kafkaSecret = KasSecret.fromSecretAttributes(this, id + 'CoraKafkaSecret', {
      secretCompleteArn: kafkaSecretArn,
      encryptionKey: globalSecretKey,
    });

    const quotaTableDbName = Constants.buildResourceName(props.stage, Constants.DYNAMODB_QUOTA_TABLE_NAME);
    const quotaTable = KasDynamodbTable.fromTableAttributes(this, 'QuotaTable', {
      tableName: quotaTableDbName,
      encryptionKey: coraGlobalDynamoKmsKey,
    });

    // Retrieve all necessary security groups
    const kafkaSecurityGroupId = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.kafkaSecurityGroupPName,
    );
    const kafkaSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'KafkaAccessSecurityGroup',
      kafkaSecurityGroupId,
    );

    // Lambda Consumer for orgs
    const quotaSyncLambdaName = `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-quota-consumer`;
    const groupId = `FRA_one_vms_cora_quota_sync_consumer_group_${props.stage}_06`;
    const quotaSyncLambda = new KafkaConsumerLambda(this, quotaSyncLambdaName, {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      description: 'Cora Lambda to sync quota from sg1 to dynamodb',
      customManagedKey: logGroupKey,
      entry: 'lambda/backend/quota/quota-sync/index.ts',
      functionName: quotaSyncLambdaName,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: groupId,
      kafkaImportTopic: props.kafkaParameters.quotaTopic,
      kafkaSecret: kafkaSecret,
      extraEnvVars: {
        QUOTA_KAFKA_TOPIC: props.kafkaParameters.quotaTopic,
        QUOTA_TABLE_NAME: quotaTable.tableName,
      },
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      kafkaSecretKmsKeyAlias: Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME),
      errHandlingLambda: logSubscriptionLambda,
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [kafkaSecurityGroup],
      stage: props.stage,
    });

    quotaTable.grantReadWriteData(quotaSyncLambda);
  }
}
