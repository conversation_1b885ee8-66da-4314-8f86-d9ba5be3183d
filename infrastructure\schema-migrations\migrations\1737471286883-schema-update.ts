import { MigrationInterface, QueryRunner } from "typeorm";

export class SchemaUpdate1737471286883 implements MigrationInterface {
    name = 'SchemaUpdate1737471286883'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "new_car_order" ADD "source_obj_id" text`);
        await queryRunner.query(`ALTER TABLE "new_car_order" ADD "source_obj_type" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "new_car_order" DROP COLUMN "source_obj_type"`);
        await queryRunner.query(`ALTER TABLE "new_car_order" DROP COLUMN "source_obj_id"`);
    }

}
