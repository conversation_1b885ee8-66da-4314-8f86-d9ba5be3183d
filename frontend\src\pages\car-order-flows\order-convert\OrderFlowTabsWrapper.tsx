import React, { useState } from 'react';
import { PTabsBar, type TabsBarUpdateEventDetail } from '@porsche-design-system/components-react';

interface OrderFlowTabsWrapperProps {
  firstTabLabel: string;
  secondTabLabel: string;
  firstTabBody: React.ReactNode;
  secondTabBody: React.ReactNode;
  activeTabIndex: number;
  onTabChange: (index: number) => void;
}

export const OrderFlowTabsWrapper: React.FC<OrderFlowTabsWrapperProps> = ({
  firstTabLabel,
  secondTabLabel,
  firstTabBody,
  secondTabBody,
  activeTabIndex,
  onTabChange,
}) => {
  const handleUpdate = (e: CustomEvent<TabsBarUpdateEventDetail>) => {
    onTabChange(e.detail.activeTabIndex);
  };

  return (
    <>
      <PTabsBar activeTabIndex={activeTabIndex} onUpdate={handleUpdate}>
        <button type="button">{firstTabLabel}</button>
        <button type="button">{secondTabLabel}</button>
      </PTabsBar>

      <div style={{ marginTop: '1rem' }}>{activeTabIndex === 0 ? firstTabBody : secondTabBody}</div>
    </>
  );
};
