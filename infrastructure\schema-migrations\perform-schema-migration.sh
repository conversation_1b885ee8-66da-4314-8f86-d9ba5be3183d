#!/bin/bash

# Fail on error
set -e

# Default aws region
AWS_REGION="eu-west-1"

# SSM Parameter name (Cluster ARN)
SSM_CLUSTER_ARN_PARAM="/cora/dev/Aurora/auroraClusterArn"

# Secret Name (DB-Admin-Secret)
SECRET_NAME="cora-aurora-admin-secret"

CLUSTER_ARN=$(aws ssm get-parameter --name "$SSM_CLUSTER_ARN_PARAM" --query 'Parameter.Value' --output text --region "$AWS_REGION")
SECRET_ARN=$(aws secretsmanager describe-secret --secret-id "$SECRET_NAME" --query 'ARN' --output text --region "$AWS_REGION")

# Export the arns as environment variables
export CLUSTER_ARN
export SECRET_ARN

# Debug: Optional - print to ensure variable is set (remove in production)
echo "ARNs received."

# Run the migration command
npm run migration:run

# Inform that the migration has completed
echo "Migration command executed successfully."
