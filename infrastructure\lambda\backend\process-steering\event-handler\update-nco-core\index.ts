import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Kas<PERSON>ambdaLog<PERSON>, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { OutboundProcessMappingModel } from '../../../../../lib/entities/outbound-mapping-model';
import {
  InboundEventHandlerEventUpdateNcoCore,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  SQSBatchResponseWithError,
  UpdateNcoCoreHandlerResult,
} from '../../../../../lib/types/process-steering-types';
import { OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { createTypeORMDataSource } from '../../../../config/typeorm-config';
import { KafkaAdapter } from '../../../../utils/kafka';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { secretCache } from '../../../../utils/secret-cache';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { getEnvVarWithAssert, pushNotificationsToKafka } from '../../../../utils/utils';
import {
  getStatusUpdateStatementsFromOutboundMapping,
  saveNcosWithAuditTrail,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { validateUpdateCoreDataRequest } from './update-nco-core-req-validation';

type UpdateNcoCoreWithMessageId = InboundEventHandlerEventUpdateNcoCore & { messageId: string };
type UpdateNcoCoreWithError = UpdateNcoCoreWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<InboundEventHandlerEventUpdateNcoCore>(
  'InboundEventHandlerEventUpdateNcoCore',
);

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const stage = getEnvVarWithAssert('STAGE');

// Table name environment variables
const coraOrgRelationTableName = getEnvVarWithAssert('TABLE_NAME_ORG_RELS');
const shippingCodeTableName = getEnvVarWithAssert('TABLE_NAME_SC');
const orderTypeTableName = getEnvVarWithAssert('TABLE_NAME_OT');
const importerTableName = getEnvVarWithAssert('TABLE_NAME_IMP');
const dealerTableName = getEnvVarWithAssert('TABLE_NAME_DLR');
const ncoValidationTables = {
  coraOrgRelationTableName,
  shippingCodeTableName,
  orderTypeTableName,
  importerTableName,
  dealerTableName,
};

const kasLambdaLogger = new KasLambdaLogger(OneVmsEventHandlerKey.UPDATE_NCO_CORE + '-event-handler', LogLevel.TRACE);

const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const auroraSecretArn = getEnvVarWithAssert('AURORA_SECRET_ARN');

const kafkaSecretArn = getEnvVarWithAssert('KAFKA_SECRET_ARN');
const eventHandlerKey = OneVmsEventHandlerKey.UPDATE_NCO_CORE;

const KAFKA_TOPIC_NOTIFICATION = getEnvVarWithAssert('KAFKA_TOPIC_NOTIFICATION');
const KAFKA_BROKERS: string[] = JSON.parse(getEnvVarWithAssert('KAFKA_BROKERS')) as string[];

const kafkaAdapter = new KafkaAdapter({
  kafka_brokers: KAFKA_BROKERS,
  kafka_secret_arn: kafkaSecretArn,
  logger: kasLambdaLogger,
});

// Initialize secret cache
secretCache.initCache(auroraSecretArn, kafkaSecretArn);

const updateNcoCoreFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  //Remember fails and successes
  const unParseableEvents: Partial<UpdateNcoCoreWithError>[] = [];
  const expectedFailedEvents: UpdateNcoCoreWithError[] = [];
  const unexpectedFailedEvents: UpdateNcoCoreWithError[] = [];
  const successfulEvents: UpdateNcoCoreWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };

  //Parse sqs events
  const updateNcoCoreEvents = event.Records.map((record) => {
    try {
      const updateNcoCoreEvent = JSON.parse(record.body) as InboundEventHandlerEventUpdateNcoCore | undefined;

      //Check if sqs content is valid
      const isValid = validateSqsEvent(updateNcoCoreEvent, logger);
      if (!isValid) {
        const message = 'SQS record is not valid, skipping';
        logger.log(LogLevel.ERROR, message, {
          data: updateNcoCoreEvent,
          correlationId: updateNcoCoreEvent?.transaction_id,
        });
        unParseableEvents.push({
          ...updateNcoCoreEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }

      return { ...updateNcoCoreEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<InboundEventHandlerEventUpdateNcoCore>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as UpdateNcoCoreWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappingRepo = (
      await createTypeORMDataSource(logger, auroraSecretArn, stage, [OutboundProcessMappingModel])
    ).getRepository(OutboundProcessMappingModel);

    const outboundEventMappings = await outboundEventMappingRepo.findBy({
      source_event_handler: eventHandlerKey,
    });
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === UpdateNcoCoreHandlerResult.SUCCESS,
    );
    // If there is no mapping, the status update statement will be empty but action is allowed
    if (outboundEventMapping) {
      outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
    }
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await pushNotificationsToKafka(
      updateNcoCoreEvents.map((updateNcoCoreEvent) =>
        eventToNotification(updateNcoCoreEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
      KAFKA_TOPIC_NOTIFICATION,
      kafkaAdapter,
      logger,
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: updateNcoCoreEvents.map((updateNcoCoreEvent) => ({
        itemIdentifier: updateNcoCoreEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await createTypeORMDataSource(logger, auroraSecretArn, stage, [
      NewCarOrderModel,
      NcoConfigurationModel,
      NcoConfigOrderedOptionsModel,
      NewCarOrderAuditTrailModel,
    ]);
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await pushNotificationsToKafka(
      updateNcoCoreEvents.map((updateNcoCoreEvent) =>
        eventToNotification(updateNcoCoreEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
      KAFKA_TOPIC_NOTIFICATION,
      kafkaAdapter,
      logger,
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: updateNcoCoreEvents.map((updateNcoCoreEvent) => ({
        itemIdentifier: updateNcoCoreEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const updateNcoCoreEvent of updateNcoCoreEvents) {
    logger.setObjectId(updateNcoCoreEvent.nco_id);
    logger.setCorrelationId(updateNcoCoreEvent.transaction_id);
    const userAttributes = updateNcoCoreEvent.user_auth_context;

    // Extract necessary attributes from auth context
    const visibilityLevel = userAttributes.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;
    const ppnId = userAttributes.organizationId;

    if (!visibilityLevel) {
      const message = 'Failed to get the visibility level';
      logger.log(LogLevel.ERROR, message, { data: event });
      expectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
      continue;
    }
    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: updateNcoCoreEvent.nco_id,
      });

      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + updateNcoCoreEvent.nco_id;
        logger.log(LogLevel.FATAL, message, { data: updateNcoCoreEvent });
        expectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
        continue;
      }

      //compare suplied modified_at with nco modified_at and reject the action if they do not match
      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== updateNcoCoreEvent.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        logger.log(LogLevel.WARN, message, { data: { sourceNco, updateNcoCoreEvent } });
        expectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
        continue;
      }

      // Validate all parameters based on users permissions and the business rules for the change of order core data.
      // If new features are implemented, the validator has to be extended.
      const _res = await validateUpdateCoreDataRequest(
        {
          dynamoDb,
          ppnId,
          payload: updateNcoCoreEvent.payload,
          sourceNco,
          tables: ncoValidationTables,
        },
        logger,
      );

      if (!_res.valid) {
        const message = 'Custom validation failed with: ' + _res.error;
        logger.log(LogLevel.WARN, message);
        expectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
        continue;
      }

      // Apply the core data update with audit trail and kafka notification
      await saveNcosWithAuditTrail(
        ncoDataSource,
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.UPDATE_CORE_DATA,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              order_type: updateNcoCoreEvent.payload.order_type!,
              modified_by: userAttributes.username,
              ...outboundStatusUpdateStatement,
            },
          );

          // Reload and return updated orders to ensure all orders were changed correctly
          return transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              order_type: updateNcoCoreEvent.payload.order_type,
            },
          });
        },
        logger,
        false,
      );

      logger.log(LogLevel.INFO, `NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was updated successfully.`);
      successfulEvents.push(updateNcoCoreEvent);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was changed by someone else since the event was created';
        expectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during cancellation';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...updateNcoCoreEvent, errorMessage: message });
      continue;
    }
  }

  //Export results into notification topic
  const successfulNotifications = successfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(
      e,
      OneVmsEventKey.UPDATE_CORE_DATA,
      NotificationStatus.EVENT_HANDLER_NIO,
      e.errorMessage,
    ),
  );

  // Unexpected fail events are not consumed and can be retried
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  // Combine all notifications
  const notificationEvents: NotificationKafkaEvent[] = [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];

  //Push notifications to kafka
  try {
    await pushNotificationsToKafka(notificationEvents, KAFKA_TOPIC_NOTIFICATION, kafkaAdapter, logger);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

function validateSqsEvent(input: InboundEventHandlerEventUpdateNcoCore | undefined, logger: KasLambdaLogger): boolean {
  const [body_validated, validation_errors] = sqsEventValidator.validate(input);
  if (body_validated === null) {
    logger.log(LogLevel.ERROR, 'ajv validation failed', { data: validation_errors });
    return false;
  }
  return true;
}

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(kasLambdaLogger)(event, context, updateNcoCoreFunc);
