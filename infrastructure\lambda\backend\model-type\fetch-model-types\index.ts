import { APIGatewayProxyEvent, APIGatewayProxyHandler, APIGatewayProxyResult, Context } from 'aws-lambda';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { getAuthContext, sendFail, sendSuccess } from '../../../utils/api-helpers';
import { getEnvVarWithAssert } from '../../../utils/utils';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { createApiGwHandler } from '../../../utils/api-gw-handler';
import { secretCache } from '../../../utils/secret-cache';
import { getAllowedDealersAndImportersWithEvent } from '../../../utils/validation-helpers';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { ModelTypeVisibilityModel } from '../../../../lib/entities/model-type-visibility-model';
import { LessThanOrEqual } from 'typeorm';

const dynamoDb = new DynamoDBClient({ region: process.env.AWS_REGION });
const applicationNameToAuthorize = getEnvVarWithAssert('APPLICATION_NAME_TO_AUTHORIZE');
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

const fetchModelTypesFunc = async (
  event: APIGatewayProxyEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<APIGatewayProxyResult> => {
  const importerNumber = event.queryStringParameters ? event.queryStringParameters['importer_number'] : undefined;
  const userAttributes = getAuthContext({ event }, logger);

  //look at the first entry for the cora application and take the visibility from there
  const visibilityLevel = userAttributes?.kasApplications[applicationNameToAuthorize]?.[0]?.modelTypeVisibility;

  //get the org id of the user from auth context
  const ppnId = userAttributes?.organizationId;

  if (!importerNumber) {
    logger.log(LogLevel.WARN, 'importerNumber query param is required but missing', { data: event });
    return sendFail(
      { message: 'Missing dealer number query parameter', status: 400, reqHeaders: event.headers },
      logger,
    );
  }

  if (!ppnId || !visibilityLevel) {
    logger.log(LogLevel.WARN, 'Failed to get the ppnId or visibility level', { data: event });
    return sendFail({ message: 'Auth context is missing', status: 401, reqHeaders: event.headers }, logger);
  }

  try {
    const allowedDealersImporters = await getAllowedDealersAndImportersWithEvent(
      {
        dynamoDb: dynamoDb,
        event: event,
      },
      logger,
    );
    const activeUserOrgs = allowedDealersImporters.uniqueDlrs.concat(allowedDealersImporters.uniqueImps);

    //find org that has importer_number set to the provided importerNumber (might be a dealer OR importer org)
    const correspondingImporterOrg = activeUserOrgs.find((org) => org.importer_number === importerNumber);
    if (correspondingImporterOrg) {
      //user has access to importer or to a dealer with corresponding importer_number, proceed with mtv query
      const dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [ModelTypeVisibilityModel]);
      const modelTypeVisibilities = await dataSource.getRepository(ModelTypeVisibilityModel).find({
        where: {
          role: visibilityLevel,
          importer_number: correspondingImporterOrg.importer_number,
          valid_from: LessThanOrEqual(new Date().toISOString().split('T', 1)[0]),
        },
      });

      if (modelTypeVisibilities.length > 0) {
        return sendSuccess(
          {
            body: modelTypeVisibilities,
            reqHeaders: event.headers,
          },
          logger,
        );
      } else {
        return sendFail({ message: 'Error loading model types', status: 404, reqHeaders: event.headers }, logger);
      }
    } else {
      return sendFail(
        {
          message: 'User is not authorized for provided importer: ' + importerNumber,
          status: 403,
          reqHeaders: event.headers,
        },
        logger,
      );
    }
  } catch (error) {
    logger.log(LogLevel.ERROR, 'Error loading model types', { data: error });
    return sendFail({ message: 'Error loading model types', status: 500, reqHeaders: event.headers }, logger);
  }
};

export const handler: APIGatewayProxyHandler = async (event, context) =>
  createApiGwHandler('fetch-model-types', LogLevel.TRACE)(event, context, fetchModelTypesFunc);
