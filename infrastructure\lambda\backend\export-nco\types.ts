import { CoraNCOConfiguration } from '../../../lib/types/new-car-order-types';
import { NotificationKafkaEvent, NotificationStatus } from '../../../lib/types/process-steering-types';

export interface NewCarOrderKafkaObject {
  ids: {
    new_car_order_id: string;
    commission_id?: string;
    business_partner_id?: string;
  };
  model_info: {
    model_type: string;
    model_year: number;
    country_code: string; // CNR
  };
  order_info: {
    base_info: {
      order_type: string;
      quota_month: string | null;
      created_by: string; // PPN Id
      last_modified_by?: string; // PPN Id
      cancellation_reason?: string;
    };
    trading_partner: {
      importer_code: string;
      importer_number: string;
      dealer_sold_to_number: string;
      dealer_ship_to_number: string; // Set to dealer_sold_to_number
    };
  };
  logistics_info: {
    shipping_code: string;
    receiving_port_code?: string;
  };
  status_info: {
    order_status_code: string;
    order_status_timestamp: string;
    order_error_status_code?: string;
    order_error_status_timestamp?: string;
    order_invoice_status_code?: string;
    order_invoice_status_timestamp?: string;
  };
  appointment_date_info: {
    production_logistic_dates: {
      order_creation_timestamp: string;
      order_last_modification_timestamp?: string;
      requested_dealer_delivery_date?: string;
    };
  };
  configuration?: CoraNCOConfiguration;
  configuration_expire?: unknown;
}

export type PvmsConfigKafkaObject = Record<string, unknown>;

export enum KafkaObjTyp {
  NCO = 'NewCarOrder',
  PVMS_CONFIG = 'PvmsConfig',
  NOTIFICATION = 'NotificationEvent',
}

export enum NcoExportActionType {
  CREATE = 'created',
  UPDATE = 'updated',
  DELETE = 'deleted',
  CANCEL = 'canceled',
  UPDATE_CORE_DATA = 'core_data.updated',
  CONVERT = 'pi.converted',
  DEALLOCATE_QUOTA = 'quota.deallocated',
  REPORT_LOSS = 'total_loss.reported',
  REVOKE_LOSS = 'total_loss.revoked',
  CREATE_IMPORTER_TRANSFER = 'importer_transfer.created',
  CANCEL_IMPORTER_TRANSFER = 'importer_transfer.canceled',
  MOVE_TO_INVENTORY = 'dealer_inventory.added',
  REMOVE_FROM_INVENTORY = 'dealer_inventory.removed',
  TRANSFER_BUY_SELL = 'transfer.buysell',
  DEALER_DIRECT_TRANSFER = 'transfer.dealer_direct',
}

export const PVMS_EXPORT_ACTION_TYPES = [
  NcoExportActionType.CREATE,
  NcoExportActionType.UPDATE,
  NcoExportActionType.CANCEL,
  NcoExportActionType.CONVERT,
];

export const P06_EXPORT_ACTION_TYPES = [
  NcoExportActionType.CREATE,
  NcoExportActionType.UPDATE,
  NcoExportActionType.DEALLOCATE_QUOTA,
  NcoExportActionType.CANCEL,
  NcoExportActionType.DEALER_DIRECT_TRANSFER,
  NcoExportActionType.TRANSFER_BUY_SELL,
  //NcoExportActionType.UPDATE_CORE_DATA, //potential future export
  //NcoExportActionType.MOVE_TO_INVENTORY, //potential future export
  //NcoExportActionType.REMOVE_FROM_INVENTORY //potential future export
];

type ActionTypeForKafkaObj<T> = T extends NewCarOrderKafkaObject | PvmsConfigKafkaObject
  ? NcoExportActionType
  : T extends NotificationKafkaEvent
    ? NotificationStatus
    : never;

type KafkaObjTypForObj<T> = T extends NewCarOrderKafkaObject
  ? KafkaObjTyp.NCO
  : T extends PvmsConfigKafkaObject
    ? KafkaObjTyp.PVMS_CONFIG
    : T extends NotificationKafkaEvent
      ? KafkaObjTyp.NOTIFICATION
      : never;

export type KafkaGenericObj = NewCarOrderKafkaObject | PvmsConfigKafkaObject | NotificationKafkaEvent | null;

export interface KafkaObj<T extends KafkaGenericObj> {
  id: string;
  kafkaActionTyp?: ActionTypeForKafkaObj<T>; //overwrite kafkaActionTyp from wrapper for individual items
  correlationid?: string; //overwrite correlationid from wrapper for individual items
  importer?: string; //overwrite importer from wrapper for individual items
  obj: T;
}

export interface KafkaObjsWrapper<T extends KafkaGenericObj> {
  kafkaObjTyp: KafkaObjTypForObj<T>;
  kafkaObjs: KafkaObj<T>[];
  kafkaActionTyp: ActionTypeForKafkaObj<T>;
}

export interface ExportResult {
  exportedIds: string[];
  failed: boolean;
  error?: Error;
}
