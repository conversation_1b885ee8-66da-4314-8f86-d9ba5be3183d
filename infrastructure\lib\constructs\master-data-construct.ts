import { Construct } from 'constructs';

import { RestApi, RequestAuthorizer, LambdaIntegration } from 'aws-cdk-lib/aws-apigateway';
import { aws_iam as iam, aws_ec2 as ec2, RemovalPolicy } from 'aws-cdk-lib';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';

import { Constants } from '../utils/constants';
import { KasDynamodbTable, KasNodejsFunction, KasKmsKey, KasStage } from '@kas-resources/constructs';
import {
  ConstantsCdk,
  LambdaDefaultBundlingExternalModules,
  LambdaTypeOrmBundlingExternalModules,
} from '../utils/constants_cdk';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';

interface MasterDataConstructProps {
  api: RestApi;
  authorizer: RequestAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  coraOrgRelTable: KasDynamodbTable;
  scTable: KasDynamodbTable;
  pcTable: KasDynamodbTable;
  otTable: KasDynamodbTable;
  dlrTable: KasDynamodbTable;
  impTable: KasDynamodbTable;
  statusMappingTable: KasDynamodbTable;
  applicationNameToAuthorize: string;
  logSubscriptionLambda: IFunction;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  auroraAccessSecurityGroup: ec2.ISecurityGroup;
  vpc: ec2.IVpc;
  auroraReaderSecret: ISecret;
  typeORMLayer: ILayerVersion;
}

export class MasterDataConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: MasterDataConstructProps) {
    super(scope, id);

    const apiResource = props.api.root.addResource('masterdata');

    // Policy to allow Lambdas to access GSI on Cora_Org_Rel_Table
    const accessCoraOrgRelGsiPolicy = new iam.PolicyStatement({
      sid: 'AllowAccessToIndex',
      effect: iam.Effect.ALLOW,
      actions: ['dynamodb:Query'],
      resources: [`${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`],
    });

    // Lambda for fetching all shipping codes
    const fetchAllShippingCodesFunction = new KasNodejsFunction(this, 'FetchAllShippingCodesFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-all-shipping-codes/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-all-shipping-codes`,
      environment: {
        TABLE_NAME_SHIPPING_CODES: props.scTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
      },
      description: 'Retrieve a list of all available shipping codes from the DynamoDB table.',
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });
    props.scTable.grantReadData(fetchAllShippingCodesFunction);
    props.coraOrgRelTable.grantReadData(fetchAllShippingCodesFunction);

    fetchAllShippingCodesFunction.addToRolePolicy(accessCoraOrgRelGsiPolicy);

    const shippingCodeResource = apiResource.addResource('shipping-code');
    const fetchAllShippingCodeLambdaIntegration = new LambdaIntegration(fetchAllShippingCodesFunction);
    shippingCodeResource.addMethod('GET', fetchAllShippingCodeLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // Lambda for fetching all port codes
    const fetchAllPortCodesFunction = new KasNodejsFunction(this, 'FetchAllPortCodesFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-all-port-codes/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-all-port-codes`,
      environment: {
        TABLE_NAME_PORT_CODES: props.pcTable.tableName,
        TABLE_NAME_DEALER: props.dlrTable.tableName,
        TABLE_NAME_IMPORTER: props.impTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Retrieve a list of all available port codes from the DynamoDB table.',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    props.pcTable.grantReadData(fetchAllPortCodesFunction);
    props.dlrTable.grantReadData(fetchAllPortCodesFunction);
    props.impTable.grantReadData(fetchAllPortCodesFunction);
    props.coraOrgRelTable.grantReadData(fetchAllPortCodesFunction);
    fetchAllPortCodesFunction.addToRolePolicy(accessCoraOrgRelGsiPolicy);

    const portCodeResource = apiResource.addResource('port-code');
    const fetchAllPortCodeLambdaIntegration = new LambdaIntegration(fetchAllPortCodesFunction);
    portCodeResource.addMethod('GET', fetchAllPortCodeLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // Lambda for fetching all order types
    const fetchAllOrderTypesFunction = new KasNodejsFunction(this, 'FetchAllOrderTypesFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-all-order-types/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-all-order-types`,
      environment: {
        TABLE_NAME_ORDER_TYPES: props.otTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Retrieve a list of all available order types from the DynamoDB table.',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    props.otTable.grantReadData(fetchAllOrderTypesFunction);
    props.coraOrgRelTable.grantReadData(fetchAllOrderTypesFunction);
    fetchAllOrderTypesFunction.addToRolePolicy(accessCoraOrgRelGsiPolicy);

    const orderTypesResource = apiResource.addResource('order-type');
    const fetchAllOrderTypesLambdaIntegration = new LambdaIntegration(fetchAllOrderTypesFunction);
    orderTypesResource.addMethod('GET', fetchAllOrderTypesLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // Lambda for fetching masterdata dealer and importer information for a dealer_number
    const fetchDealerImporterFunction = new KasNodejsFunction(this, 'FetchDealerImporterFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-dealer-importer/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-dealer-importer`,
      environment: {
        TABLE_NAME_DEALER: props.dlrTable.tableName,
        TABLE_NAME_IMPORTER: props.impTable.tableName,
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Get masterdata dealer and importer information for a dealer, after checking user access',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup],
      stage: props.stage,
    });

    props.dlrTable.grantReadData(fetchDealerImporterFunction);
    props.impTable.grantReadData(fetchDealerImporterFunction);
    props.coraOrgRelTable.grantReadData(fetchDealerImporterFunction);

    //allow access to gsi to query parent_ppn_id column
    fetchDealerImporterFunction.addToRolePolicy(accessCoraOrgRelGsiPolicy);

    const dealerImporterResource = apiResource.addResource('oc-dealer-importer');
    const fetchDealerImporterIntegration = new LambdaIntegration(fetchDealerImporterFunction);
    dealerImporterResource.addMethod('GET', fetchDealerImporterIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.querystring.dealer_number': true,
      },
    });

    // Lambda for fetching all onevms status
    const getStatusDescriptionsHandler = new KasNodejsFunction(this, 'FetchAllOnevmsStatusFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-all-onevms-status/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-all-onevms-status`,
      environment: {
        CORS_DOMAIN: props.corsDomain,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Function to get status descriptions .',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.vpcEndpointsSecurityGroup, props.auroraAccessSecurityGroup],
      stage: props.stage,
    });
    props.auroraReaderSecret.grantRead(getStatusDescriptionsHandler);
    const onevmsStatusResource = apiResource.addResource('onevms-status');
    const onevmsStatusLambdaIntegration = new LambdaIntegration(getStatusDescriptionsHandler);
    onevmsStatusResource.addMethod('GET', onevmsStatusLambdaIntegration, {
      authorizer: props.authorizer,
    });

    // Lambda for fetching all inbound status mappings
    const fetchAllInboundStatusMappingsFunction = new KasNodejsFunction(this, 'FetchAllInboundStatusMappingsFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/masterdata/fetch-all-inbound-mappings/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-all-inbound-mappings`,
      environment: {
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
        CORS_DOMAIN: props.corsDomain,
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Retrieve a list of all inbound status mappings from coraMd.',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.auroraAccessSecurityGroup],
      stage: props.stage,
      layers: [props.typeORMLayer],
    });

    props.auroraReaderSecret.grantRead(fetchAllInboundStatusMappingsFunction);

    const inboundStatusMappingsResource = apiResource.addResource('inbound-mapping');
    const inboundStatusMappingsLambdaIntegration = new LambdaIntegration(fetchAllInboundStatusMappingsFunction);
    inboundStatusMappingsResource.addMethod('GET', inboundStatusMappingsLambdaIntegration, {
      authorizer: props.authorizer,
    });
  }
}
