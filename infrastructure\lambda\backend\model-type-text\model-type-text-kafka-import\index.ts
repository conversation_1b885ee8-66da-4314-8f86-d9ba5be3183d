import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'aws-lambda';
import { ObjectValidator } from '../../../../lib/utils/object-validation';
import { KafkaModelTypeText, KeyValueModelTypeText } from '../../../../lib/types/model-type-text-types';
import { Kas<PERSON><PERSON>bdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import { correlationHeader, getEnvVarWithAssert } from '../../../utils/utils';
import { createTypeORMDataSource } from '../../../config/typeorm-config';
import { ModelTypeTextModel } from '../../../../lib/entities/model-type-text-model';
import { secretCache } from '../../../utils/secret-cache';
import { DataSource } from 'typeorm';

const IMPORT_TOPIC = getEnvVarWithAssert('IMPORT_TOPIC');
const stage = getEnvVarWithAssert('STAGE');

//initialize secret chache with required secrets
const AURORA_SECRET_ARN = getEnvVarWithAssert('AURORA_SECRET_ARN');
secretCache.initCache(AURORA_SECRET_ARN);

let dataSource: DataSource | undefined = undefined;
const objectValidator = new ObjectValidator<KafkaModelTypeText>('KafkaModelTypeText');
const logger = new KasLambdaLogger('model-type-text-kafka-import', LogLevel.TRACE);

export const handler: Handler<MSKEvent, void> = async (event, context) => {
  logger.setRequestContext(context);
  logger.log(LogLevel.TRACE, `Got topic event, processing...`, {
    data: event.records,
    correlationId: context.awsRequestId,
  });

  const keyValueModelTypeTexts = (
    Object.entries(event.records)
      .filter(([key]) => key.includes(IMPORT_TOPIC))
      .flatMap(([, value]) => value.map(parseRecord))
      .filter(Boolean) as KeyValueModelTypeText[]
  ) //remove null values
    .sort((a, b) => b.timestamp - a.timestamp); //sort descending by kafka timestamp for duplicate removal later

  logger.setCorrelationId(context.awsRequestId);
  if (keyValueModelTypeTexts.length > 0) {
    logger.log(LogLevel.INFO, 'Successfully parsed events', { data: keyValueModelTypeTexts });
  } else {
    logger.log(LogLevel.WARN, `No objects left after parsing and filtering, exiting lambda`);
    return;
  }

  await insertModelTypeTexts(keyValueModelTypeTexts);
};

function getActionType(record: MSKRecord): string {
  for (const header of record.headers) {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (header.ce_type) {
      return Buffer.from(header.ce_type).toString();
    }
  }
  //return created by default since all headers are missing in kafka
  return 'created';
}

function parseRecord(record: MSKRecord): KeyValueModelTypeText | null {
  try {
    const kafkaKey = Buffer.from(record.key, 'base64').toString('utf8');
    logger.setCorrelationId(correlationHeader(record));

    const actionType = getActionType(record);
    const kafkaModelBuff: Buffer = Buffer.from(record.value, 'base64');
    const kafkaModelObj = JSON.parse(kafkaModelBuff.toString('utf8')) as unknown;

    const [body_validated, validation_errors] = objectValidator.validate(kafkaModelObj);
    if (body_validated === null) {
      logger.log(LogLevel.WARN, 'Object validation failed, skipping object', {
        data: { error: validation_errors, object: kafkaModelObj },
      });
      return null;
    }

    return {
      key: kafkaKey,
      value: body_validated,
      timestamp: record.timestamp,
      actionType,
    };
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Could not parse model type text, skipping object.', {
      data: { error: e, object: record.value },
    });
    return null;
  }
}

async function insertModelTypeTexts(kvMtts: KeyValueModelTypeText[]): Promise<void> {
  // Track seen mtt keys to avoid duplicate entries.
  const seen = new Set<string>();
  const uniqueMtts = kvMtts.filter((keyValueMtt) => {
    //add languageCountryIsoCode to key because kafka has duplicate keys for different language codes...
    const key = keyValueMtt.key + keyValueMtt.value.values[0].orderTypeTexts[0].languageCountryIsoCode;
    // Check whether the mtt key has been seen
    if (!seen.has(key)) {
      seen.add(key); // Add the mtt key to the set.
      return true; // Keep the item.
    }
    return false; // Discard the item.
  });

  try {
    if (!dataSource) {
      dataSource = await createTypeORMDataSource(logger, AURORA_SECRET_ARN, stage, [ModelTypeTextModel]);
    }

    //TODO no headers are present in inbound topic atm, so nothing will ever be deleted for now
    const actionTypesToAdd = ['created', 'updated'];
    const actionTypesToDelete = ['deleted'];

    const mttsToAdd = uniqueMtts
      .filter((kvMtt) => actionTypesToAdd.includes(kvMtt.actionType))
      .flatMap<ModelTypeTextModel>(kafkaKvMttToCoraMtts);

    const mttsToDelete = uniqueMtts
      .filter((kvMtt) => actionTypesToDelete.includes(kvMtt.actionType))
      .flatMap<ModelTypeTextModel>(kafkaKvMttToCoraMtts);

    await dataSource.getRepository(ModelTypeTextModel).save(mttsToAdd);
    logger.log(LogLevel.INFO, `Successfully inserted or updated ${mttsToAdd.length} model type texts into RDS`);
    await dataSource.getRepository(ModelTypeTextModel).remove(mttsToDelete);
    logger.log(LogLevel.INFO, `Successfully deleted ${mttsToDelete.length} model type texts from RDS`);
  } catch (error) {
    logger.log(LogLevel.ERROR, 'DB Error while updating model type texts, throwing lambda', {
      data: { error: error },
    });
    throw error;
  }
}

const kafkaKvMttToCoraMtts = (kvMtt: KeyValueModelTypeText): ModelTypeTextModel[] => {
  //kvMtt.value.values should have length = 1, but just in case...
  return kvMtt.value.values.flatMap((orderTypeValue) =>
    orderTypeValue.orderTypeTexts.map((orderTypeText) => ({
      iso_language_code: orderTypeText.languageCountryIsoCode,
      ggid: kvMtt.value.ggId,
      model_year: kvMtt.value.my4,
      model_type: kvMtt.value.orderType,
      language_code: orderTypeText.languageCode,
      model_text: orderTypeText.text,
      importer_number: kvMtt.value.kasData[0].importerNr ?? null,
      cnr: kvMtt.value.kasData[0].cNr ?? null,
      modified_by: 'PCCD_INBOUND_KAFKA',
      created_by: 'PCCD_INBOUND_KAFKA',
    })),
  );
};
