import React, { PropsWithChildren, useContext, useEffect, useState } from 'react';
import { EXISTING_PERMISSIONS } from '../../../infrastructure/lib/utils/constants';
import { useGetAuthorizedDealersQuery } from '../store/api/BossApi';
import { useGetPermissionsForUserQuery } from '../store/api/RbamPermissionApi';
import { AuthorizedDealer, AuthorizedImporter } from '../store/types';

type HasPermission = Partial<Record<keyof typeof EXISTING_PERMISSIONS, boolean>>;
interface AuthContextType {
  permissions: HasPermission;
  isLoading: boolean;
  importers: AuthorizedImporter[];
  dealers: AuthorizedDealer[];
}

interface AuthContextProviderProps {}

const AuthContext = React.createContext<AuthContextType>({} as AuthContextType);

const AuthContextProvider: React.FC<PropsWithChildren<AuthContextProviderProps>> = (props) => {
  const [permissions, setPermissions] = useState<HasPermission>({});
  const userPermissionQuery = useGetPermissionsForUserQuery(undefined);
  //add the dealer part here has >1 importer
  const { data: dealers } = useGetAuthorizedDealersQuery(undefined);
  const [dealersList, setDealersList] = useState<AuthorizedDealer[]>([]);
  const [importersList, setImportersList] = useState<AuthorizedImporter[]>([]);
  useEffect(() => {
    if (dealers) {
      const uniqImpNrs = new Set<string>();
      const importerList: AuthorizedImporter[] = [];
      for (const dlr of dealers) {
        if (uniqImpNrs.has(dlr.importer_number)) {
          continue;
        }
        // add here
        uniqImpNrs.add(dlr.importer_number);
        importerList.push({
          importer_number: dlr.importer_number,
          display_name: dlr.importer_display_name,
          role: dlr.role,
        });
      }
      const dealerList = dealers.map((dealer) => ({
        ...dealer,
      }));
      setDealersList(dealerList);
      setImportersList(importerList);
    }
  }, [dealers]);
  useEffect(() => {
    const rbam_app_permissions = userPermissionQuery.data?.data[0].app_permissions;
    const _permissions: HasPermission = {
      NCO_CREATE: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_CREATE),
      NCO_COPY: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_COPY),
      NCO_EDIT: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_EDIT),
      NCO_MULTI_EDIT: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_MULTI_EDIT),
      NCO_CANCEL: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_CANCEL),
      NCO_DEALLOCATE_QUOTA: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_DEALLOCATE_QUOTA),
      NCO_LIST: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_LIST),
      NCO_TRANSFER_IMPORTER:
        rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_TRANSFER_IMPORTER) && importersList.length > 1,
      NCO_REPORT_TOTAL_LOSS: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_REPORT_TOTAL_LOSS),
      NCO_REVOKE_TOTAL_LOSS: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_REVOKE_TOTAL_LOSS),
      PI_LIST: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.PI_LIST),
      MON_AUDIT_TRAIL: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.MON_AUDIT_TRAIL),
      MON_ORDER_STATUS: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.MON_ORDER_STATUS),
      NCO_MOVE_TO_DEALER_INVENTORY: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.NCO_MOVE_TO_DEALER_INVENTORY),
      NCO_REMOVE_FROM_DEALER_INVENTORY: rbam_app_permissions?.includes(
        EXISTING_PERMISSIONS.NCO_REMOVE_FROM_DEALER_INVENTORY,
      ),
      BUY_SELL_TRANSFER: rbam_app_permissions?.includes(EXISTING_PERMISSIONS.BUY_SELL_TRANSFER),
    };
    setPermissions(_permissions);
  }, [userPermissionQuery.data, importersList]);

  return (
    <AuthContext.Provider
      value={{
        permissions: permissions,
        isLoading: userPermissionQuery.isFetching,
        importers: importersList,
        dealers: dealersList,
      }}
    >
      {props.children}
    </AuthContext.Provider>
  );
};

const useAuthContext = (): AuthContextType => useContext(AuthContext);

export { AuthContext, useAuthContext };

export default AuthContextProvider;
