import { <PERSON><PERSON><PERSON>, <PERSON>electWrapper, PText } from '@porsche-design-system/components-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useLazyGetModelTypeTextsQuery } from '../../../store/api/BossApi';
import { AppDispatch } from '../../../store/configureStore';
import { getValidQuotaMonthForConfig, matchMttForModelTypeAndModelYear } from '../../../utils/utils';
import { OrderTypeForm } from '../form-elements/OrderTypeForm';
import { PortCodeForm } from '../form-elements/PortCodeForm';
import { QuotaForm } from '../form-elements/QuotaForm';
import { RequestedDeliveryDateForm } from '../form-elements/RequestedDeliveryDateForm';
import { ShippingCodeForm } from '../form-elements/ShippingCodeForm';
import './OrderConvertForm.css';
import { ConvertPiApiRequest, OneVmsEventKey } from '../../../../../infrastructure/lib/types/process-steering-types';
import { useGetCarOrdersForPIConnectMutation, useGetNewCarOrderByIdQuery } from '../../../store/api/NewCarOrderApi';
import { selectKccConfig } from '../../../store/slices/KccConfigSlice';
import { useAppSelector } from '../../../app/hooks';
import { CoraNCPurchaseIntentionApiResponse } from '../../../../../infrastructure/lib/types/purchase-intention-types';
import { CoraNCOQueryApiRequestBody } from '../../../../../infrastructure/lib/types/new-car-order-types';
import { fetchAgGridSsrmNewCarOrder } from '../../../store/api/ServerSideDataSource';
import { CarOrderInList } from '../../../store/types';
const neededOrderConvertRequestKeys = [
  'model_type',
  'model_year',
  'importer_number',
  'dealer_number',
  'cnr',
  'importer_code',
] as const;
type NeededOrderConvertRequestKeys = (typeof neededOrderConvertRequestKeys)[number];
//o sa fie dropdown si o sa am taburi,trebuie sa modific end poointul sa pot trimite filtru pt quota valid from untill si sa las comentariu la ticketul de backend
//sa fac e2e ,label pt erori
export type FormApiNewCarOrderConvertRequest = Partial<ConvertPiApiRequest> &
  Pick<ConvertPiApiRequest, NeededOrderConvertRequestKeys> & {
    connected_order_id?: string;
  };

interface OrderConnectFormProps {
  setError: (err?: string) => void;
  connected_order_id: string;
  setConnectedOrderId: (id: string) => void;
  partialNewCarOrder: FormApiNewCarOrderConvertRequest;
}

export const OrderConnectForm: React.FC<OrderConnectFormProps> = ({
  connected_order_id,
  setConnectedOrderId,
  setError,
  partialNewCarOrder,
}) => {
  const { t, i18n } = useTranslation();
  const [formState, setFormState] = useState<FormApiNewCarOrderConvertRequest>(partialNewCarOrder);
  const dispatch = useDispatch<AppDispatch>();
  const [loadModelTypeTexts, { data: modelTypeTexts, isLoading: isModelTypeTextsLoading }] =
    useLazyGetModelTypeTextsQuery();
  const [modelTypeText, setModelTypeText] = useState<string>(t('not_loaded'));
  useEffect(() => {
    if (modelTypeTexts) {
      const modelTypeText = matchMttForModelTypeAndModelYear(
        modelTypeTexts,
        formState.model_type,
        formState.model_year,
        t,
      );
      setModelTypeText(modelTypeText ?? t('missing_translation'));
    }
  }, [modelTypeTexts]);
  ////
  const kccConfig = useAppSelector(selectKccConfig);
  const [firstValidQuotaMonth, setFirstValidQuotaMonth] = useState<string>(new Date().toISOString().slice(0, 7));
  const [lastValidQuotaMonth, setLastValidQuotaMonth] = useState<string>(
    new Date('9999-12-01').toISOString().slice(0, 7),
  );
  //i how i do this?first i need to have the nco and after that i can check if it s valid....tffffff (-.-)
  //
  //i am getting valid from and valid untill based on the kcc config and the existing configuration of new car order selected
  useEffect(() => {
    if (kccConfig.config) {
      const { from, until } = getValidQuotaMonthForConfig(kccConfig.config);
      console.log('from', from);
      console.log('until', until);
      setFirstValidQuotaMonth(from);
      setLastValidQuotaMonth(until);
    }
  }, [kccConfig.config]);
  const [ncoList, setNcoList] = useState<CarOrderInList[]>([]);
  const [errMessage, setErrMessage] = useState<string | undefined>(undefined);
  const [isLoadingNcos, setIsLoadingNcos] = useState(false);

  const fetchNCOsManually = async ({
    dealerNumber,
    quotaFrom,
    quotaTo,
  }: {
    dealerNumber: string;
    quotaFrom: string;
    quotaTo: string;
  }) => {
    const body: CoraNCOQueryApiRequestBody = {
      startRow: 0,
      endRow: 100,
      eventFilters: [{ event: OneVmsEventKey.UPDATE_NCO }],
      filterModel: {
        dealer_number: {
          filterType: 'text',
          type: 'equals',
          filter: dealerNumber,
        },
        quota_month: {
          filterType: 'range',
          filterFrom: quotaFrom,
          filterTo: quotaTo,
        },
      },
      sortModel: [],
    };

    return await fetchAgGridSsrmNewCarOrder(body);
  };
  useEffect(() => {
    console.log('firstValidQuotaMonth', firstValidQuotaMonth);
    console.log('lastValidQuotaMonth', lastValidQuotaMonth);
    const isQuotaRangeSet =
      firstValidQuotaMonth !== new Date().toISOString().slice(0, 7) &&
      lastValidQuotaMonth !== new Date('9999-12-01').toISOString().slice(0, 7);
    console.log('isQuotaRangeSet', isQuotaRangeSet);
    const shouldFetch = isQuotaRangeSet && !!formState.dealer_number;

    //if (!shouldFetch) return; it s ok like this?

    const fetchData = async () => {
      try {
        setIsLoadingNcos(true); //loading
        const result = await fetchNCOsManually({
          dealerNumber: formState.dealer_number!,
          quotaFrom: firstValidQuotaMonth,
          quotaTo: lastValidQuotaMonth,
        });
        console.log('NCO result:', result);
        if (result?.data) {
          setNcoList(result.data as CarOrderInList[]);
        }
      } catch (error) {
        console.error('Failed to fetch NCOs:', error);
        setError(t('error_fetching_nco'));
      } finally {
        setIsLoadingNcos(false); // stop loading
      }
    };
    fetchData();
  }, [firstValidQuotaMonth, lastValidQuotaMonth, formState.dealer_number]);

  return (
    <>
      <div>
        <div className="order-convert-details-single-container">
          <div className="field-col">
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_type_text')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {modelTypeText}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_year')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.model_year}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('dealer_number')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.dealer_number}{' '}
              </PText>
            </div>
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('dealer_name')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.dealer_name}{' '}
              </PText>
            </div>
          </div>
          <div className="field-col">
            <div className="field-row">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('model_type')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.model_type}
              </PText>
            </div>
            <div className="field-row" data-e2e="od_business_partner_id">
              <PText color="contrast-medium" size={'xx-small'}>
                {t('business_partner_id')}
              </PText>
              <PText weight={'bold'} size={'x-small'}>
                {formState.business_partner_id}
              </PText>
            </div>
          </div>
        </div>
      </div>

      <PDivider></PDivider>
      <div className="form-container1">
        <PSelectWrapper
          data-e2e="SelectNCO"
          style={{ flexBasis: 'calc(50% - 10px)' }}
          label={t('select_nco_prompt')}
          state={errMessage ? 'error' : 'none'}
          message={errMessage}
        >
          <select
            value={connected_order_id ?? ''}
            onChange={(e) => setConnectedOrderId(e.target.value)}
            disabled={isLoadingNcos || ncoList.length === 0}
          >
            {!connected_order_id && (
              <option disabled value="">
                {isLoadingNcos ? `${t('loading')}...` : t('select_nco_placeholder')}
              </option>
            )}

            {!isLoadingNcos &&
              ncoList.map((order) => (
                <option key={order.pk_new_car_order_id} value={order.pk_new_car_order_id}>
                  {`${order.pk_new_car_order_id} | ${order.quota_month ?? 'no quota'} | ${order.order_type}`}
                </option>
              ))}
          </select>
        </PSelectWrapper>
      </div>
    </>
  );
};
