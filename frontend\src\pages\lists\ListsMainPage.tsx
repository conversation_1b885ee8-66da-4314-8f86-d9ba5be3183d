import { PTabsBar, TabsBarUpdateEvent } from '@porsche-design-system/components-react';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { NavLink, Outlet, useLocation } from 'react-router-dom';
import { useAuthContext } from '../../app/AuthContext';
import { routes } from '../../Constants';
import { useFeatureFlagContext } from '../../app/FeatureFlagContext';

const ListsMainPage: React.FC = () => {
  const { t } = useTranslation();

  const [activeTab, setActiveTab] = useState<number | undefined>(undefined);
  const onTabChange = useCallback((e: CustomEvent<TabsBarUpdateEvent>) => {
    setActiveTab(e.detail.activeTabIndex);
  }, []);

  const authContext = useAuthContext();
  const featureFlagContext = useFeatureFlagContext();

  return (
    <div>
      <nav style={{ height: '5vh' }}>
        <PTabsBar activeTabIndex={activeTab} onUpdate={onTabChange} size="small">
          <NavLink data-e2e="display_purchase_intentions" to={routes.lists.openSales}>
            {t('open_sales')}
          </NavLink>
          <NavLink data-e2e="display_purchase_intentions" to={routes.lists.purchaseIntentions}>
            {t('purchase_intentions')}
          </NavLink>
          <NavLink data-e2e="display_orders" to={routes.lists.preProductionOrders}>
            {t('pre_production_orders')}
          </NavLink>
          <NavLink data-e2e="display_orders_in_production" to={routes.lists.inProductionOrders}>
            {t('in_production_orders')}
          </NavLink>
          <NavLink data-e2e="display_orders_in_distribution" to={routes.lists.inDistributionOrders}>
            {t('in_distribution_orders')}
          </NavLink>
          <NavLink data-e2e="display_orders_at_dealerships" to={routes.lists.atDealershipsOrders}>
            {t('at_dealership_orders')}
          </NavLink>
          {featureFlagContext.featureFlags.reportRevokeTotalLoss && authContext.permissions.NCO_REVOKE_TOTAL_LOSS && (
            <NavLink data-e2e="display_orders_in_total_loss" to={routes.lists.totalLossOrders}>
              {t('total_loss_orders')}
            </NavLink>
          )}
        </PTabsBar>
      </nav>
      <Outlet />
    </div>
  );
};
export default ListsMainPage;
