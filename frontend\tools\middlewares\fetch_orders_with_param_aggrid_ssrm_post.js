// const fs = require('fs');
// const path = require('path');
// const prefix = '/new-car-order/get';

// module.exports = function (req, res, next) {
//   if (req.method === 'POST' && req.url.startsWith(prefix) && !!req.body) {
//     const _send = res.send;

//     // Lade den gesamten Datensatz aus der db.json Datei
//     fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
//       if (err) {
//         return _send.call(this, JSON.stringify({ message: 'Internal Server Error' }), 500);
//       }

//       try {
//         const json = JSON.parse(data);
//         let ncos = json['new-car-order'];
//         const filters = req.body;
//         const sortModel = filters.sortModel;
//         const filterModel = filters.filterModel;
//         const _status = json['onevms-status'];

//         // Filters
//         for (const [k, v] of Object.entries(filterModel)) {
//           if (v.filterType === 'set') {
//             ncos = ncos.filter((nco) => v.values.includes(nco[k]));
//           } else {
//             ncos = ncos.filter((nco) => nco[k]?.includes(v.filter));
//           }
//         }

//         // Sorting
//         if (sortModel && sortModel[0]) {
//           if (sortModel[0].sort === 'asc') {
//             ncos.sort((a, b) => (a[sortModel[0].colId] < b[sortModel[0].colId] ? -1 : 1));
//           } else {
//             ncos.sort((a, b) => (a[sortModel[0].colId] < b[sortModel[0].colId] ? 1 : -1));
//           }
//         }

//         // Pagination
//         ncos = ncos.slice(filters.startRow, filters.endRow);
//         const filteredData = { data: ncos, rowCount: -1 };

//         if (filteredData.data.length > 0) {
//           return _send.call(res, JSON.stringify(filteredData));
//         } else {
//           return _send.call(res, JSON.stringify({ message: 'No matching records found' }), 404);
//         }
//       } catch (e) {
//         return _send.call(res, JSON.stringify({ message: 'Internal Server Error' }), 500);
//       }
//     });
//   } else {
//     next();
//   }
// };
const fs = require('fs');
const path = require('path');
const prefix = '/new-car-order/get';

module.exports = function (req, res, next) {
  if (req.method === 'POST' && req.url.startsWith(prefix) && !!req.body) {
    const _send = res.send;

    fs.readFile(path.join(__dirname, '../db.json'), 'utf8', (err, data) => {
      if (err) {
        return res.status(500).send(JSON.stringify({ message: 'Internal Server Error' }));
      }

      try {
        const json = JSON.parse(data);
        let ncos = json['new-car-order'];
        const filters = req.body;
        const sortModel = filters.sortModel;
        const filterModel = filters.filterModel;
        const actionFilters = filters.actionFilters;
        const _status = json['onevms-status'];

        // Action Filters
        if (actionFilters?.changeable) {
          ncos = ncos.filter(
            (nco) =>
              _status.find(
                (s) =>
                  s.one_vms_status === nco.order_status_onevms_code &&
                  (s.one_vms_error_status ?? 'null') === (nco.order_status_onevms_error_code ?? 'null'),
              )?.order_changeable,
          );
        }

        // Filters
        for (const [k, v] of Object.entries(filterModel || {})) {
          if (!v || !v.filterType) continue;

          switch (v.filterType) {
            case 'set':
              ncos = ncos.filter((nco) => v.values.includes(nco[k]));
              break;

            case 'text':
              ncos = ncos.filter((nco) => {
                const val = (nco[k] ?? '').toString().toLowerCase();
                const filter = (v.filter ?? '').toString().toLowerCase();
                switch (v.type) {
                  case 'equals':
                    return val === filter;
                  case 'notEqual':
                    return val !== filter;
                  case 'contains':
                    return val.includes(filter);
                  case 'notContains':
                    return !val.includes(filter);
                  case 'startsWith':
                    return val.startsWith(filter);
                  case 'endsWith':
                    return val.endsWith(filter);
                  default:
                    return true;
                }
              });
              break;

            case 'range':
              ncos = ncos.filter((nco) => {
                const val = nco[k];
                if (val === undefined || val === null) return false;
                if (v.filterFrom && val < v.filterFrom) return false;
                if (v.filterTo && val > v.filterTo) return false;
                return true;
              });
              break;

            case 'join':
              ncos = ncos.filter((ncoItem) => {
                const itemVal = (ncoItem[k] ?? '').toString().toLowerCase();
                const results = v.conditions.map((cond) => {
                  const filter = (cond.filter ?? '').toString().toLowerCase();
                  switch (cond.type) {
                    case 'equals':
                      return itemVal === filter;
                    case 'notEqual':
                      return itemVal !== filter;
                    case 'contains':
                      return itemVal.includes(filter);
                    case 'notContains':
                      return !itemVal.includes(filter);
                    case 'startsWith':
                      return itemVal.startsWith(filter);
                    case 'endsWith':
                      return itemVal.endsWith(filter);
                    default:
                      return true;
                  }
                });
                return v.operator === 'AND' ? results.every(Boolean) : results.some(Boolean);
              });
              break;
          }
        }

        // Sorting
        if (sortModel && sortModel[0]) {
          const { colId, sort } = sortModel[0];
          ncos.sort((a, b) => (sort === 'asc' ? (a[colId] < b[colId] ? -1 : 1) : a[colId] < b[colId] ? 1 : -1));
        }

        // Pagination
        ncos = ncos.slice(filters.startRow ?? 0, filters.endRow ?? ncos.length);
        const filteredData = { data: ncos, rowCount: -1 };

        if (filteredData.data.length > 0) {
          return res.send(JSON.stringify(filteredData));
        } else {
          return res.status(404).send(JSON.stringify({ message: 'No matching records found' }));
        }
      } catch (e) {
        console.error('Parsing or filtering error:', e);
        return res.status(500).send(JSON.stringify({ message: 'Internal Server Error' }));
      }
    });
  } else {
    next();
  }
};
