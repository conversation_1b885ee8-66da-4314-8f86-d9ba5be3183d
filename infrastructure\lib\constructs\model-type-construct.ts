import { Construct } from 'constructs';

import { Duration, RemovalPolicy, aws_secretsmanager, aws_ec2 as ec2, aws_iam as iam } from 'aws-cdk-lib';
import { IResource, LambdaIntegration, RequestAuthorizer } from 'aws-cdk-lib/aws-apigateway';
import { IFunction, ILayerVersion } from 'aws-cdk-lib/aws-lambda';

import {
  KafkaConsumerLambda,
  KasDynamodbTable,
  KasKmsKey,
  KasNodejsFunction,
  KasStage,
} from '@kas-resources/constructs';
import { ISecurityGroup } from 'aws-cdk-lib/aws-ec2';
import { AuthenticationMethod } from 'aws-cdk-lib/aws-lambda-event-sources';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaTypeOrmBundlingExternalModules } from '../utils/constants_cdk';

interface ModelTypeConstructProps {
  authorizer: RequestAuthorizer;
  stage: KasStage;
  corsDomain: string;
  logGroupKey: KasKmsKey;
  typeormLayer: ILayerVersion;
  auroraAccessSecurityGroup: ISecurityGroup;
  auroraReaderSecret: ISecret;
  auroraWriterSecret: ISecret;
  coraOrgRelTable: KasDynamodbTable;
  applicationNameToAuthorize: string;
  parentResource: IResource;
  kafkaParameters: {
    brokers: string[];
    hubModelTypeVisibilityTopic: string;
    kafkaSecret: aws_secretsmanager.ISecret;
  };
  logSubscriptionLambda: IFunction;
  vpcEndpointsSecurityGroup: ec2.ISecurityGroup;
  kafkaSecurityGroup: ec2.ISecurityGroup;
  vpc: ec2.IVpc;
}

export class ModelTypeConstruct extends Construct {
  public constructor(scope: Construct, id: string, props: ModelTypeConstructProps) {
    super(scope, id);

    // Lambda Consumer for model type visibilities sync from boss
    const mtvSyncLambda = new KafkaConsumerLambda(this, 'mtv-sync-lambda', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      description: 'Cora Lambda to sync model type visibility table',
      customManagedKey: props.logGroupKey,
      entry: 'lambda/backend/model-type/boss-modeltype-visibility-sync/index.ts',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-model-type-visibility-sync`,
      kafkaBrokers: props.kafkaParameters.brokers,
      kafkaImportGroup: `FRA_one_vms_cora_modeltype_visibility_consumer_group_${props.stage}_01_new`,
      kafkaImportTopic: props.kafkaParameters.hubModelTypeVisibilityTopic,
      kafkaSecret: props.kafkaParameters.kafkaSecret,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      extraEnvVars: {
        AURORA_SECRET_ARN: props.auroraWriterSecret.secretArn,
      },
      eventSourceAuthenticationMethod: AuthenticationMethod.BASIC_AUTH,
      kafkaSecretKmsKeyAlias: Constants.buildKmsKeyId(props.stage, Constants.KMS_KEY_GLOBAL_SECRET_NAME),
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      // Default of 128 almost reached 122 avg
      memorySize: 256,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.kafkaSecurityGroup, props.auroraAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });

    props.auroraWriterSecret.grantRead(mtvSyncLambda);

    const modelTypeResource = props.parentResource.addResource('model-type');

    // Lambda for fetching filtered model types based on users visibility
    const fetchModelTypesFunction = new KasNodejsFunction(this, 'FetchModelTypesFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/model-type/fetch-model-types/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-model-types`,
      environment: {
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
        STAGE: props.stage,
      },
      timeout: Duration.seconds(10),
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description: 'Retrieve a list of model types based on the users visibility from the DynamoDB table.',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.kafkaSecurityGroup, props.auroraAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });
    props.auroraReaderSecret.grantRead(fetchModelTypesFunction);

    props.coraOrgRelTable.grantReadData(fetchModelTypesFunction);

    //allow access to gsi to query parent_ppn_id column
    fetchModelTypesFunction.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [
          `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
        ],
      }),
    );

    const fetchModelTypesFunctionIntegration = new LambdaIntegration(fetchModelTypesFunction);
    modelTypeResource.addMethod('GET', fetchModelTypesFunctionIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.querystring.importer_number': true,
      },
    });

    const modelTypeTextsResource = modelTypeResource.addResource('text');
    const modelTypeTextResource = modelTypeTextsResource.addResource('{isoLanguageCode}');

    // Lambda for fetching filtered model types based on users visibility
    const fetchModelTypeTextsFunction = new KasNodejsFunction(this, 'FetchModelTypetextsFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      entry: 'lambda/backend/model-type-text/fetch-model-type-texts/index.ts',
      handler: 'handler',
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-fetch-model-type-texts`,
      environment: {
        TABLE_NAME_ORG_RELS: props.coraOrgRelTable.tableName,
        CORS_DOMAIN: props.corsDomain,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        AURORA_SECRET_ARN: props.auroraReaderSecret.secretArn,
        STAGE: props.stage,
      },
      timeout: Duration.seconds(25),
      memorySize: 512,
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      description:
        'Retrieve a list of model type texts based on the provided iso language code and the users visibility level.',
      customManagedKey: props.logGroupKey,
      errHandlingLambda: props.logSubscriptionLambda,
      vpc: props.vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_SERVICES },
      securityGroups: [props.kafkaSecurityGroup, props.auroraAccessSecurityGroup],
      bundling: {
        externalModules: LambdaTypeOrmBundlingExternalModules,
      },
      layers: [props.typeormLayer],
      stage: props.stage,
    });
    props.auroraReaderSecret.grantRead(fetchModelTypeTextsFunction);
    props.coraOrgRelTable.grantReadData(fetchModelTypeTextsFunction);

    //allow access to gsi to query parent_ppn_id column
    fetchModelTypeTextsFunction.addToRolePolicy(
      new iam.PolicyStatement({
        sid: 'AllowAccessToIndex',
        effect: iam.Effect.ALLOW,
        actions: ['dynamodb:Query'],
        resources: [
          `${props.coraOrgRelTable.tableArn}/index/${Constants.DYNAMODB_CORA_ORG_REL_TABLE_PARAMS.pPIDIndex}`,
        ],
      }),
    );

    const fetchModelTypeTextsFunctionIntegration = new LambdaIntegration(fetchModelTypeTextsFunction);
    modelTypeTextResource.addMethod('GET', fetchModelTypeTextsFunctionIntegration, {
      authorizer: props.authorizer,
      requestParameters: {
        'method.request.path.isoLanguageCode': true,
      },
    });
  }
}
