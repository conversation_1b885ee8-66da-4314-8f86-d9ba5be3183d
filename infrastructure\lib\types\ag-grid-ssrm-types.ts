export interface SortModelItem {
  /** Column Id to apply the sort to. */
  colId: string;
  sort: 'asc' | 'desc';
}

export type TextAdvancedFilterModelType =
  | 'equals'
  | 'notEqual'
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith';
// | 'blank'
// | 'notBlank';

export type FilterModel = TextFilterModel | SetFilterModel | JoinFilterModel | RangeFilterModel;

export interface TextFilterModel {
  filterType: 'text';
  type: TextAdvancedFilterModelType;
  filter: string;
}

export interface SetFilterModel {
  filterType: 'set';
  values: string[];
}

export interface JoinFilterModel {
  filterType: string;
  operator: 'AND' | 'OR';
  conditions: TextFilterModel[];
}
//used for filtering quota months
export interface RangeFilterModel {
  filterType: 'range';
  filterFrom?: string;
  filterTo?: string;
}
