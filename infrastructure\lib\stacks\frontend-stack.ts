import {
  InternalDeveloperFirewallConstruct,
  KasAssetSource,
  KasDefaultLogRetentions,
  KasKmsKey,
  KasNodejsFunction,
  KasS3Bucket,
} from '@kas-resources/constructs';
import {
  aws_certificatemanager as acm,
  aws_apigateway as apigateway,
  aws_ssm,
  CfnOutput,
  Duration,
  aws_ec2 as ec2,
  aws_iam as iam,
  aws_logs as logs,
  RemovalPolicy,
  aws_route53 as route53,
  aws_route53_targets as route53Targets,
  Size,
  Stack,
} from 'aws-cdk-lib';
import { BucketDeployment, ServerSideEncryption } from 'aws-cdk-lib/aws-s3-deployment';
import { Construct } from 'constructs';
import * as path from 'path';
import { CoraStackPropsWithVpc } from '../types_cdk/cdk-types';
import { Constants } from '../utils/constants';
import { ConstantsCdk, LambdaDefaultBundlingExternalModules } from '../utils/constants_cdk';
import { GlobalStack } from './global-stack';

// ESM
// import { fileURLToPath } from 'url';
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);

export class FrontendStack extends Stack {
  public constructor(scope: Construct, id: string, props: CoraStackPropsWithVpc) {
    super(scope, id, props);

    const vpc = props.vpc;

    const logSubscriptionLambda = GlobalStack.getLogSubscriptionLambda(this);
    //import global loggroup kms
    const logGroupKmsKeyArn = aws_ssm.StringParameter.valueForStringParameter(
      this,
      props.globalParameterNames.logGroupKmsKeyArnPName,
    );
    const logGroupKey = KasKmsKey.fromKeyArn(this, 'logGroupKey', logGroupKmsKeyArn);

    // Create an S3 bucket for your React app
    const frontendBucket = new KasS3Bucket(this, 'CoraSourceBucket', {
      removalPolicy: RemovalPolicy.DESTROY,
      autoDeleteObjects: true,
    });

    //create deployment role fpr bucket deployment
    const deploymentRole = new iam.Role(this, 'CoraWebBucketDeploymentRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      description: 'Used by BucketDeployment lambda for website deployment',
    });
    frontendBucket.grantReadWrite(deploymentRole);

    // Make a BucketDeployment to upload the frontend assets to the S3 bucket
    new BucketDeployment(this, 'DeployWebsite', {
      sources: [
        KasAssetSource.asset(path.join(__dirname, '../../../frontend/dist')),
        KasAssetSource.jsonData('config.json', {
          stage: props.stage,
          ppnRolesWrite: props.ppnRolesWrite,
          featureFlags: props.featureFlags,
        }),
        KasAssetSource.jsonData('storno.json', Constants.CANCELLATION_REASONS),
      ],
      destinationBucket: frontendBucket,
      role: deploymentRole,
      serverSideEncryption: ServerSideEncryption.AWS_KMS,
    });

    // Import the hosted zone for the domain
    const hostedZone = route53.HostedZone.fromLookup(this, 'HostedZone', {
      domainName: props.hostedZoneName,
    });

    // Create a certificate for the domain
    const domainForFrontend = `${props.hostedZoneName}`;
    const certificate = new acm.Certificate(this, 'Certificate', {
      domainName: domainForFrontend,
      validation: acm.CertificateValidation.fromDns(hostedZone),
    });

    // Create an IAM role for API Gateway
    const executeRole = new iam.Role(this, 'ApiGatewayS3AssumeRole', {
      assumedBy: new iam.ServicePrincipal('apigateway.amazonaws.com'),
    });

    // Grant the necessary permissions to access the S3 bucket
    frontendBucket.grantRead(executeRole);

    const apiGwLoggGroup = new logs.LogGroup(this, 'apiGwLogGroup', {
      logGroupName: props.stage + 'coraFrontendApiAccessLogs',
      retention: KasDefaultLogRetentions[props.stage],
      encryptionKey: logGroupKey,
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    // Create the API Gateway
    const api = new apigateway.RestApi(this, `${props.stage}cora-frontend`, {
      domainName: {
        domainName: domainForFrontend,
        certificate: certificate,
      },
      restApiName: 'Website API Gateway Cora',
      description: `Website API Gateway for Cora ${props.stage}`,
      binaryMediaTypes: ['*/*'],
      minCompressionSize: Size.bytes(0),
      cloudWatchRole: true,
      deployOptions: {
        accessLogDestination: new apigateway.LogGroupLogDestination(apiGwLoggGroup),
        accessLogFormat: apigateway.AccessLogFormat.jsonWithStandardFields(),
        loggingLevel: apigateway.MethodLoggingLevel.INFO,
        throttlingRateLimit: 500,
        throttlingBurstLimit: 1000,
      },
      policy: new iam.PolicyDocument({
        statements: [
          new iam.PolicyStatement({
            actions: ['execute-api:Invoke'],
            resources: ['*'],
            effect: iam.Effect.ALLOW,
            principals: [new iam.AnyPrincipal()],
          }),
        ],
      }),
    });

    // Create a security group for the authorizer flow
    const authorizerSecurityGroup = new ec2.SecurityGroup(this, 'FrontendAuthorizerSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: false,
      securityGroupName: 'FrontendAuthorizerSecurityGroup',
    });
    authorizerSecurityGroup.addEgressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(443));

    // Create a Lambda function for the authorizer
    const authorizerFunction = new KasNodejsFunction(this, 'LambdaAuthorizer', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-frontend-authorizer`,
      entry: 'lambda/frontend/authorizer/index.ts',
      handler: 'handler',
      timeout: Duration.seconds(10),
      environment: {
        ISSUER: props.idp.issuer,
        PUBLIC_KEY_ENDPOINT: props.idp.publicKeyUrl,
        CLIENT_ID: props.idp.clientId,
        KAS_AUTH_ENDPOINT_URL: props.kasAuthEndpointUrl,
        APPLICATION_NAME_TO_AUTHORIZE: props.applicationNameToAuthorize,
        PPN_ROLES_WRITE: JSON.stringify(props.ppnRolesWrite),
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      customManagedKey: logGroupKey,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_AUTH },
      securityGroups: [authorizerSecurityGroup],
      stage: props.stage,
    });

    // Create a Lambda function for logout operation
    const logoutFunction = new KasNodejsFunction(this, 'LogoutFunction', {
      runtime: ConstantsCdk.NODE_JS_VERSION,
      functionName: `${Constants.APPLICATION_SHORT_NAME}-${props.stage}-logout`,
      entry: 'lambda/frontend/logout/index.ts', // path to your logout function
      handler: 'handler',
      timeout: Duration.seconds(10),
      environment: {
        REDIRECT_URL: `https://ppnlite.porsche.com/idp/startSLO.ping`, // redirect to PPN after logout
        COOKIE_DOMAIN: 'dpp.porsche.com',
      },
      logGroupRemovalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
      customManagedKey: logGroupKey,
      errHandlingLambda: logSubscriptionLambda,
      bundling: {
        externalModules: LambdaDefaultBundlingExternalModules,
      },
      vpc: vpc,
      vpcSubnets: { subnetGroupName: Constants.PRIVATE_SUBNET_GROUPNAME_AUTH },
      securityGroups: [authorizerSecurityGroup],
      stage: props.stage,
    });

    // Create RequestAuthorizer for the API Gateway
    // Note that the identity source is the 'Cookie' header and not the 'Authorization' header
    // This is because there is no option to validate only parts of the Cookie header in API Gateway
    const authorizer = new apigateway.RequestAuthorizer(this, 'Authorizer', {
      handler: authorizerFunction,
      identitySources: [apigateway.IdentitySource.header('Cookie')], // Use the 'Cookie' header as the identity source
      resultsCacheTtl: Duration.minutes(5), // Cache duration for the authorizer
    });

    // Create an API Gateway integration with the S3 service to serve the 'index.html' file from the frontend bucket.
    const s3IndexIntegration = new apigateway.AwsIntegration({
      service: 's3',
      integrationHttpMethod: 'GET',
      path: `${frontendBucket.bucketName}/index.html`,
      options: {
        credentialsRole: executeRole,
        integrationResponses: [
          {
            statusCode: '200',
            responseParameters: {
              'method.response.header.Content-Type': 'integration.response.header.Content-Type',
            },
          },
        ],
      },
    });

    // Add a 'GET' method to the API Gateway root resource that uses the 's3IndexIntegration' for serving the 'index.html' file.
    api.root.addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //test route for dcc
    api.root.addResource('test').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //path for testing
    api.root.addResource('testing').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //path orders
    const orderPath = api.root.addResource('orders');
    orderPath.addResource('{proxy+}').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //path lists
    const listPath = api.root.addResource('lists');
    listPath.addResource('{proxy+}').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //path orders
    const auditPath = api.root.addResource('audit');
    auditPath.addResource('{proxy+}').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //path monitoring
    api.root.addResource('monitoring').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    //navigation-system
    api.root.addResource('navigation-system').addMethod('GET', s3IndexIntegration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    // Create another API Gateway integration with the S3 service to serve all other files from the frontend bucket.
    const s3Integration = new apigateway.AwsIntegration({
      service: 's3',
      integrationHttpMethod: 'GET',
      path: `${frontendBucket.bucketName}/{proxy}`,
      options: {
        credentialsRole: executeRole,
        integrationResponses: [
          {
            statusCode: '200',
            responseParameters: {
              'method.response.header.Content-Type': 'integration.response.header.Content-Type',
            },
          },
        ],
        requestParameters: {
          'integration.request.path.proxy': 'method.request.path.proxy',
        },
      },
    });

    // Add a new resource with a '{proxy+}' path variable to the API Gateway root resource and associate it with the 's3Integration'.
    api.root.addResource('{proxy+}').addMethod('GET', s3Integration, {
      methodResponses: [
        {
          statusCode: '200',
          responseParameters: {
            'method.response.header.Content-Type': true,
          },
        },
      ],
      requestParameters: {
        'method.request.path.proxy': true,
        'method.request.header.Content-Type': true,
      },
      authorizer,
    });

    // Create a new resource for the '/logout' path
    const logoutResource = api.root.addResource('logout');

    // Integration options for logout
    const logoutIntegrationOptions = {
      methodResponses: [
        {
          statusCode: '302',
          responseParameters: {
            'method.response.header.Set-Cookie': true, // Allow Set-Cookie header
            'method.response.header.Location': true, // Allow Location header
          },
        },
      ],
      authorizer,
    };

    // Add a method to the logout resource associated with the logoutFunction
    logoutResource.addMethod('GET', new apigateway.LambdaIntegration(logoutFunction), logoutIntegrationOptions);

    // Create a new resource that handles the redirect to the IDP
    new apigateway.GatewayResponse(this, 'AccessDeniedResponse', {
      restApi: api,
      type: apigateway.ResponseType.ACCESS_DENIED,
      // responseHeaders: {
      //   Location: `'${props.idp.loginUrl}?client_id=${props.idp.clientId}&response_type=code&scope=openid&redirect_uri=https%3A%2F%2F${props.hostedZoneName}/auth'`,
      // },
      statusCode: '401',
      templates: { 'application/json': '{"message": "You are not authorized to access this application"}' },
    });

    const redirectUrl = `${props.kasAuthEndpointUrl}/refreshauth`;

    // Create a new resource that handles the redirect to the IDP
    new apigateway.GatewayResponse(this, 'UnauthenticatedResponse', {
      restApi: api,
      type: apigateway.ResponseType.UNAUTHORIZED,
      responseHeaders: {
        Location: `'${redirectUrl}'`,
      },
      statusCode: '302',
      templates: {},
    });

    // Add a Route53 record set for the API endpoint
    new route53.ARecord(this, 'ApiGatewayAliasRecord', {
      zone: hostedZone,
      target: route53.RecordTarget.fromAlias(new route53Targets.ApiGateway(api)),
      ttl: Duration.minutes(5),
    });

    // Output the S3 bucket URL and API Gateway URL
    new CfnOutput(this, 'BucketURL', {
      value: frontendBucket.bucketWebsiteUrl,
    });

    // Output the API Gateway URL
    new CfnOutput(this, 'ApiGatewayURL', {
      value: api.url,
    });

    const wafLogGroup = new logs.LogGroup(this, 'developerFirewallFrontendLogGroup', {
      logGroupName: 'aws-waf-logs-' + props.stage + 'coraFrontendWAFTrafficLogs',
      //overwrite default for dev and int because big data be expensive
      retention: props.stage !== 'prod' ? logs.RetentionDays.ONE_WEEK : KasDefaultLogRetentions['prod'],
      encryptionKey: logGroupKey,
      removalPolicy: props.stage === 'prod' ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
    });

    // WAF that blocks all requests except for the ones coming from the internal developer network
    new InternalDeveloperFirewallConstruct(this, 'InternalDeveloperFirewall', api, wafLogGroup);
  }
}
