import { Context, SQSBatchResponse, SQSEvent, SQSHandler } from 'aws-lambda';
import { Constants, OneVmsEventHandlerKey } from '../../../../../lib/utils/constants';
import { KasLambdaLogger, LogLevel } from '@kas-resources/constructs/src/lib/kas-lambda-logger';
import {
  NcoConfigOrderedOptionsModel,
  NcoConfigurationModel,
  NewCarOrderModel,
} from '../../../../../lib/entities/new-car-order-model';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { NewCarOrderAuditTrailModel } from '../../../../../lib/entities/new-car-order-audit-trail-model';
import {
  SystemOrderActionEventHandlerEvent,
  NotificationKafkaEvent,
  NotificationStatus,
  OneVmsEventKey,
  OneVmsSourceSystemKey,
  DefaultEventHandlerResult,
  SQSBatchResponseWithError,
} from '../../../../../lib/types/process-steering-types';
import {
  saveNcosWithAuditTrail,
  getStatusUpdateStatementsFromOutboundMapping,
  StatusUpdateStatement,
} from '../../../../utils/utils-typeorm';
import { NcoExportActionType } from '../../../export-nco/types';
import { ObjectValidator } from '../../../../../lib/utils/object-validation';
import { eventToNotification, unparseableEventToNotification } from '../../../../utils/process-steering-helpers';
import { createSqsEventHandlerWithInitLogger } from '../../../../utils/sqs-event-handler';
import { EventHandlerContext } from '../event-handler-context';

type ScheduledCancelNcoWithMessageId = SystemOrderActionEventHandlerEvent & {
  nco_id: string;
  messageId: string;
};
type ScheduledCancelNcoWithError = ScheduledCancelNcoWithMessageId & { errorMessage: string };

const sqsEventValidator = new ObjectValidator<SystemOrderActionEventHandlerEvent>('SystemOrderActionEventHandlerEvent');

EventHandlerContext.init(OneVmsEventHandlerKey.CANCEL_NCO_SCHEDULED, [
  NewCarOrderModel,
  NewCarOrderAuditTrailModel,
  NcoConfigurationModel,
  NcoConfigOrderedOptionsModel,
]);

const scheduledCancelNewCarOrdersFunc = async (
  event: SQSEvent,
  context: Context,
  logger: KasLambdaLogger,
): Promise<SQSBatchResponse | void> => {
  const unParseableEvents: Partial<ScheduledCancelNcoWithError>[] = [];
  const expectedFailedEvents: ScheduledCancelNcoWithError[] = [];
  const unexpectedFailedEvents: ScheduledCancelNcoWithError[] = [];
  const successfulEvents: ScheduledCancelNcoWithMessageId[] = [];
  const sqsBatchResponse: SQSBatchResponseWithError = {
    batchItemFailures: [],
  };
  const scheduledCancelNcoEvents = event.Records.map((record) => {
    try {
      // Parse body from event object
      const scheduledCancelNcoEvent = JSON.parse(record.body) as SystemOrderActionEventHandlerEvent | undefined;
      const [body_validated, validation_errors] = sqsEventValidator.validate(scheduledCancelNcoEvent);
      if (body_validated === null) {
        const message = 'SQS record is not valid, ajv validation failed';
        logger.log(LogLevel.WARN, message, { data: validation_errors });
        unParseableEvents.push({
          ...scheduledCancelNcoEvent,
          errorMessage: message,
          messageId: record.messageId,
        });
        return undefined;
      }
      return { ...scheduledCancelNcoEvent, messageId: record.messageId };
    } catch (e) {
      const message = 'Failed to parse sqs record body, skipping';
      logger.log(LogLevel.ERROR, message, {
        data: { error: e, body: record.body },
      });
      unParseableEvents.push({
        ...(JSON.parse(record.body) as Partial<SystemOrderActionEventHandlerEvent>),
        errorMessage: message,
        messageId: record.messageId,
      });
      return undefined;
    }
  }).filter(Boolean) as ScheduledCancelNcoWithMessageId[];

  //Get outbound event mapping config and status update statements
  let outboundStatusUpdateStatement: StatusUpdateStatement = {};
  try {
    const outboundEventMappings = await EventHandlerContext.getOutboundEventMappings();
    const outboundEventMapping = outboundEventMappings.find(
      // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
      (mapping) => mapping.event_result === DefaultEventHandlerResult.SUCCESS,
    );
    // If there is no mapping, the action is aborted because Cancel NCO needs a status change.
    if (!outboundEventMapping) {
      throw new Error(
        `No outbound mapping found for successful result of event handler ${EventHandlerContext.eventHandlerKey}.`,
      );
    }
    outboundStatusUpdateStatement = getStatusUpdateStatementsFromOutboundMapping(outboundEventMapping);
  } catch (e) {
    const message = `Failed to get outbound event mapping config`;
    logger.log(LogLevel.ERROR, message, {
      data: e,
    });
    await EventHandlerContext.pushNotificationsToKafka(
      scheduledCancelNcoEvents.map((scheduledCancelEvent) =>
        eventToNotification(scheduledCancelEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no outboundMapping, no party.
    return {
      batchItemFailures: scheduledCancelNcoEvents.map((scheduledCancelEvent) => ({
        itemIdentifier: scheduledCancelEvent.messageId,
        errorMessage: message,
      })),
    };
  }
  let ncoDataSource: DataSource;
  let ncoRepo: Repository<NewCarOrderModel>;

  try {
    ncoDataSource = await EventHandlerContext.getDataSource();
    ncoRepo = ncoDataSource.getRepository(NewCarOrderModel);
  } catch (e) {
    const message = 'Unexpected error. Datasource could not be initialized';
    logger.log(LogLevel.ERROR, message, { data: e });

    await EventHandlerContext.pushNotificationsToKafka(
      scheduledCancelNcoEvents.map((scheduledCancelEvent) =>
        eventToNotification(scheduledCancelEvent, NotificationStatus.EVENT_HANDLER_NIO, message),
      ),
    );
    // Everyone goes into the DLQ because no DataSource, no party.
    return {
      batchItemFailures: scheduledCancelNcoEvents.map((scheduledCancelEvent) => ({
        itemIdentifier: scheduledCancelEvent.messageId,
        errorMessage: message,
      })),
    };
  }

  //Process all events one by one
  for (const scheduledCancelNcoEvent of scheduledCancelNcoEvents) {
    logger.setObjectId(scheduledCancelNcoEvent.nco_id);
    logger.setCorrelationId(scheduledCancelNcoEvent.transaction_id);

    try {
      // Fetch source nco from the database
      const sourceNco = await ncoRepo.findOneBy({
        pk_new_car_order_id: scheduledCancelNcoEvent.nco_id,
      });

      // Validate request according to business logic
      if (!sourceNco) {
        const message = 'Failed to find order with id: ' + scheduledCancelNcoEvent.nco_id;
        logger.log(LogLevel.FATAL, message, { data: scheduledCancelNcoEvent });
        expectedFailedEvents.push({ ...scheduledCancelNcoEvent, errorMessage: message });
        continue;
      }

      if (sourceNco.quota_month !== null) {
        const message = `Quota on NewCarOrder is set again. Cancellation is aborted.`;
        logger.log(LogLevel.WARN, message, { data: sourceNco });
        expectedFailedEvents.push({ ...scheduledCancelNcoEvent, errorMessage: message });
        continue;
      }

      //compare supplied modified_at with nco modified_at and reject the action if they do not match
      if (new Date(sourceNco.modified_at ?? 0).toISOString() !== scheduledCancelNcoEvent.modified_at) {
        const message = 'Nco was changed by someone else since the event was created';
        logger.log(LogLevel.WARN, message, { data: { sourceNco, scheduledCancelNcoEvent } });
        expectedFailedEvents.push({ ...scheduledCancelNcoEvent, errorMessage: message });
        continue;
      }

      await saveNcosWithAuditTrail(
        ncoDataSource,
        [sourceNco.pk_new_car_order_id],
        NcoExportActionType.CANCEL,
        async (transactionManager: EntityManager) => {
          await transactionManager.getRepository(NewCarOrderModel).update(
            { pk_new_car_order_id: sourceNco.pk_new_car_order_id },
            {
              cancellation_reason: Constants.CANCELLATION_REASONS[1].id, //will be 'A2' as long as hardcoded order does not change...
              changed_by_system: OneVmsSourceSystemKey.CORA,
              order_status_onevms_timestamp_last_change: new Date().toISOString(),
              modified_by: 'cora_technical',
              ...outboundStatusUpdateStatement,
            },
          );
          return await transactionManager.getRepository(NewCarOrderModel).find({
            where: {
              pk_new_car_order_id: sourceNco.pk_new_car_order_id,
              cancellation_reason: Constants.CANCELLATION_REASONS[1].id,
            },
          });
        },
        logger,
        false,
      );
      logger.log(LogLevel.INFO, `NewCarOrder with NCO id ${sourceNco.pk_new_car_order_id} was cancelled successfully.`);
      successfulEvents.push(scheduledCancelNcoEvent);
    } catch (error) {
      const err = error as { message: string; name: string };
      if (
        err.name === 'QueryFailedError' &&
        err.message.includes('could not serialize access due to concurrent update')
      ) {
        const message = 'Nco was concurrently changed by someone else.';
        expectedFailedEvents.push({ ...scheduledCancelNcoEvent, errorMessage: message });
        continue;
      }
      const message = 'Unexpected database error occurred during cancellation';
      logger.log(LogLevel.ERROR, message, { data: error });
      unexpectedFailedEvents.push({ ...scheduledCancelNcoEvent, errorMessage: message });
      continue;
    }
  }
  //Export results into notification topic
  const successfulNotifications = successfulEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_IO),
  );

  const expectedFailedNotifications = expectedFailedEvents.map((e) =>
    eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage),
  );

  const unparseableNotifications = unParseableEvents.map((e) =>
    unparseableEventToNotification(
      e,
      OneVmsEventKey.CANCEL_SCHEDULED,
      NotificationStatus.EVENT_HANDLER_NIO,
      e.errorMessage,
    ),
  );

  // Unexpected fail events are not consumed and can be retried
  const unexpectedNotifications = unexpectedFailedEvents.map((e) => {
    sqsBatchResponse.batchItemFailures.push({
      itemIdentifier: e.messageId,
      errorMessage: e.errorMessage,
    });
    return eventToNotification(e, NotificationStatus.EVENT_HANDLER_NIO, e.errorMessage);
  });

  // Alle Notifications zusammenführen
  const notificationEvents: NotificationKafkaEvent[] = [
    ...successfulNotifications,
    ...expectedFailedNotifications,
    ...unparseableNotifications,
    ...unexpectedNotifications,
  ];

  //Push notifications to kafka
  try {
    await EventHandlerContext.pushNotificationsToKafka(notificationEvents);
  } catch (e) {
    logger.log(LogLevel.ERROR, 'Failed to push notifications to kafka', { data: e });
  }

  //Return fails so that events are put into dlq
  return sqsBatchResponse;
};

export const handler: SQSHandler = async (event, context) =>
  createSqsEventHandlerWithInitLogger(EventHandlerContext.logger)(event, context, scheduledCancelNewCarOrdersFunc);
